2025-08-19 08:45:20.141 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\Backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x00000279AD78A8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\Backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x00000279AFCD8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x00000279AFCDBA60>
    └ <uvicorn.server.Server object at 0x00000279B6508EF0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000279AFCDBB00>
           │       │   └ <uvicorn.server.Server object at 0x00000279B6508EF0>
           │       └ <function run at 0x00000279AF51F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000279B7B83AE0>
           │      └ <function Runner.run at 0x00000279AF9BB2E0>
           └ <asyncio.runners.Runner object at 0x00000279B69CBF50>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000279AF9B8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000279B69CBF50>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x00000279AFA88D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000279AF9BAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000279AF514860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1716, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 64279)>
    └ <_ProactorSocketTransport closing fd=1716>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-19 08:45:20.360 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\Backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x00000279AD78A8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\Backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x00000279AFCD8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x00000279AFCDBA60>
    └ <uvicorn.server.Server object at 0x00000279B6508EF0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000279AFCDBB00>
           │       │   └ <uvicorn.server.Server object at 0x00000279B6508EF0>
           │       └ <function run at 0x00000279AF51F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000279B7B83AE0>
           │      └ <function Runner.run at 0x00000279AF9BB2E0>
           └ <asyncio.runners.Runner object at 0x00000279B69CBF50>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000279AF9B8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000279B69CBF50>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x00000279AFA88D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000279AF9BAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000279AF514860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1888, family=2, type=1, proto=6, laddr=('*************', 56372), raddr=('*************', 5981)>
    └ <_ProactorSocketTransport closing fd=1888>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-19 08:45:20.370 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\Backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x00000279AD78A8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\Backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x00000279AFCD8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x00000279AFCDBA60>
    └ <uvicorn.server.Server object at 0x00000279B6508EF0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000279AFCDBB00>
           │       │   └ <uvicorn.server.Server object at 0x00000279B6508EF0>
           │       └ <function run at 0x00000279AF51F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000279B7B83AE0>
           │      └ <function Runner.run at 0x00000279AF9BB2E0>
           └ <asyncio.runners.Runner object at 0x00000279B69CBF50>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000279AF9B8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000279B69CBF50>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x00000279AFA88D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000279AF9BAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000279AF514860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1892, family=2, type=1, proto=6, laddr=('*************', 56368), raddr=('*************', 5981)>
    └ <_ProactorSocketTransport closing fd=1892>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-19 09:26:13.089 | ERROR    | 539d643fcfa240eb95978dcaaeb8b972 | 创建知识库失败: RAGFlow服务连接失败: 200: Unsupported model: <bge-m3:latest@Ollama>
2025-08-19 09:26:15.618 | ERROR    | e6e10b90040d42c79b6162b9e89ad3d2 | 创建知识库失败: RAGFlow服务连接失败: 200: Unsupported model: <bge-m3:latest@Ollama>
2025-08-19 09:26:16.438 | ERROR    | 4f36690724b54637b253f556d65c0ab5 | 创建知识库失败: RAGFlow服务连接失败: 200: Unsupported model: <bge-m3:latest@Ollama>
2025-08-19 09:26:16.913 | ERROR    | 1454eefb1cc641b29c5dbb570bc7d150 | 创建知识库失败: RAGFlow服务连接失败: 200: Unsupported model: <bge-m3:latest@Ollama>
2025-08-19 09:26:17.370 | ERROR    | 9611f50408ee49f3967dfa6df77697c8 | 创建知识库失败: RAGFlow服务连接失败: 200: Unsupported model: <bge-m3:latest@Ollama>
2025-08-19 09:26:17.726 | ERROR    | e8cad2c4ff9f4367a890ea6aa67d9d03 | 创建知识库失败: RAGFlow服务连接失败: 200: Unsupported model: <bge-m3:latest@Ollama>
2025-08-19 09:26:18.090 | ERROR    | 6de0c78153c647109c047ef0f773ebb8 | 创建知识库失败: RAGFlow服务连接失败: 200: Unsupported model: <bge-m3:latest@Ollama>
2025-08-19 09:34:53.185 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\Backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x00000279AD78A8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\Backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x00000279AFCD8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x00000279AFCDBA60>
    └ <uvicorn.server.Server object at 0x00000279B6508EF0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000279AFCDBB00>
           │       │   └ <uvicorn.server.Server object at 0x00000279B6508EF0>
           │       └ <function run at 0x00000279AF51F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000279B7B83AE0>
           │      └ <function Runner.run at 0x00000279AF9BB2E0>
           └ <asyncio.runners.Runner object at 0x00000279B69CBF50>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000279AF9B8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000279B69CBF50>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x00000279AFA88D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000279AF9BAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000279AF514860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1972, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 58637)>
    └ <_ProactorSocketTransport closing fd=1972>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-19 09:53:52.405 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\Backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x00000279AD78A8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\Backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x00000279AFCD8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x00000279AFCDBA60>
    └ <uvicorn.server.Server object at 0x00000279B6508EF0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000279AFCDBB00>
           │       │   └ <uvicorn.server.Server object at 0x00000279B6508EF0>
           │       └ <function run at 0x00000279AF51F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000279B7B83AE0>
           │      └ <function Runner.run at 0x00000279AF9BB2E0>
           └ <asyncio.runners.Runner object at 0x00000279B69CBF50>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000279AF9B8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000279B69CBF50>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x00000279AFA88D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000279AF9BAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000279AF514860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1924, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 52108)>
    └ <_ProactorSocketTransport closing fd=1924>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-19 09:55:30.285 | ERROR    | ******************************** | 创建知识库失败: RAGFlow服务连接失败: 200: Unsupported model: <bge-m3:latest@Ollama>
2025-08-19 09:55:33.691 | ERROR    | e940de79087346ed9c25cee46dca8bd0 | 创建知识库失败: RAGFlow服务连接失败: 200: Unsupported model: <bge-m3:latest@Ollama>
2025-08-19 10:08:03.773 | ERROR    | a19549a6c22244b2afc5c9786e267e15 | 创建知识库失败: RAGFlow服务连接失败: 200: Unsupported model: <bge-m3:latest@Ollama>
2025-08-19 10:10:11.654 | ERROR    | f66faea702b8491cb9e1c554956ac068 | 创建知识库失败: RAGFlow服务连接失败: 200: Unsupported model: <bge-m3:latest@Ollama>
2025-08-19 10:18:20.618 | ERROR    | 5715bfb937aa4aa4896560d635fc75ff | 创建知识库失败: RAGFlow服务连接失败: 200: Unsupported model: <bge-m3:latest@Ollama>
2025-08-19 11:37:44.847 | ERROR    | ba10d882517b49079a7caf863f7ce557 | 创建知识库失败: RAGFlow服务连接失败: 200: Unsupported model: <bge-m3:latest@Ollama>
2025-08-19 11:50:22.227 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\Backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x000001BCFAB0A8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\Backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x000001BCFD0D8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000001BCFD0DBA60>
    └ <uvicorn.server.Server object at 0x000001BCFADCD970>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001BCFD0DBB00>
           │       │   └ <uvicorn.server.Server object at 0x000001BCFADCD970>
           │       └ <function run at 0x000001BCFC91F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001BC84F56A40>
           │      └ <function Runner.run at 0x000001BCFC9BB2E0>
           └ <asyncio.runners.Runner object at 0x000001BC83C8C3E0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001BCFC9B8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001BC83C8C3E0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000001BCFCA88D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001BCFC9BAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BCFC914860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1896, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 61475)>
    └ <_ProactorSocketTransport closing fd=1896>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-19 11:55:03.733 | ERROR    | d1ded92fb43b4e7b9c3a134275fad3e9 | 创建知识库失败: RAGFlow服务连接失败: 200: Unsupported model: <bge-m3:latest@Ollama>
2025-08-19 11:57:25.824 | ERROR    | 8bca2bc570f048bca68efc453cdf9757 | 创建知识库失败: RAGFlow服务连接失败: 200: Unsupported model: <bge-m3:latest@Ollama>
2025-08-19 13:31:45.022 | ERROR    | ******************************** | 创建知识库失败: RAGFlow服务连接失败: 200: Unsupported model: <text-embedding-bge-m3@LM-Studio>
2025-08-19 13:32:58.727 | ERROR    | 0e25f598a54048b0ae72c319dbebae17 | 创建知识库失败: RAGFlow服务连接失败: 200: Unsupported model: <bge-m3:latest@Ollama>
2025-08-19 13:33:34.334 | ERROR    | 90d57a86a38b41ed8f0492cdf3c42020 | 创建知识库失败: RAGFlow服务连接失败: 200: Unsupported model: <bge-m3:latest@Ollama>
2025-08-19 13:34:53.387 | ERROR    | 75a9b9d29cd44d14ba466f6ecd534d00 | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-19 13:34:54.380 | ERROR    | 88247eabc6f246268fcec1dc25b5ecfc | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-19 13:34:54.850 | ERROR    | afc1fe9a679c4f5b95073a807481c0cf | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-19 13:34:55.131 | ERROR    | bb23a05a79e44477a8892be90c5ed3b9 | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-19 13:34:55.499 | ERROR    | 6da2178367094455824c7fa9aeb4958d | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-19 13:35:15.827 | ERROR    | 91664892a78c4344ba421d3885e35cc6 | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-19 13:35:20.705 | ERROR    | 6fa401bf314e4f80bf74a784bef857f0 | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-19 13:43:10.260 | ERROR    | bd388ff306964c0ea22a7608a47af9ec | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-19 13:43:26.604 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\Backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x000002877BC8A8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\Backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x000002877E178B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000002877E17BA60>
    └ <uvicorn.server.Server object at 0x0000028704EFBB60>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002877E17BB00>
           │       │   └ <uvicorn.server.Server object at 0x0000028704EFBB60>
           │       └ <function run at 0x000002877D9BF060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000028706046A40>
           │      └ <function Runner.run at 0x000002877DA5B2E0>
           └ <asyncio.runners.Runner object at 0x0000028704FF7740>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002877DA58EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000028704FF7740>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002877DB28D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002877DA5AC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002877D9B4860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1656, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 50807)>
    └ <_ProactorSocketTransport closing fd=1656>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-19 13:43:39.429 | ERROR    | 7bb59e5eb8d542039d333778fd3fee12 | 更新知识库失败: RAGFlow服务连接失败: 200: Unsupported model: <bge-m3:latest@Ollama>
2025-08-19 13:57:37.848 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\Backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x000002877BC8A8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\Backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x000002877E178B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000002877E17BA60>
    └ <uvicorn.server.Server object at 0x0000028704EFBB60>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002877E17BB00>
           │       │   └ <uvicorn.server.Server object at 0x0000028704EFBB60>
           │       └ <function run at 0x000002877D9BF060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000028706046A40>
           │      └ <function Runner.run at 0x000002877DA5B2E0>
           └ <asyncio.runners.Runner object at 0x0000028704FF7740>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002877DA58EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000028704FF7740>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002877DB28D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002877DA5AC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002877D9B4860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1776, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 57446)>
    └ <_ProactorSocketTransport closing fd=1776>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-19 13:57:40.314 | ERROR    | 0307855735034efab4e0898322deb6f8 | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-19 13:57:41.204 | ERROR    | 641723bb35614034a025e0a1858a323e | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-19 13:57:41.625 | ERROR    | 048d5f724c6e474bbfb9593e6389f642 | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-19 13:58:39.048 | ERROR    | 457fe1877e004d5f8a7bdbcfbb0593fb | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-19 13:59:09.962 | ERROR    | a949326dae99410b955cd17daff743cd | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-19 14:07:30.869 | ERROR    | 187e3f7c250f419d8d86bab6c7781a68 | 获取知识库列表失败: RAGFlow服务连接失败: 200: Field: <orderby> - Message: <Input should be 'create_time' or 'update_time'> - Value: <pagerank>
2025-08-19 14:07:43.993 | ERROR    | 8320458e337e4a02bbfb06c2585ca8c0 | 获取知识库列表失败: RAGFlow服务连接失败: 200: Field: <orderby> - Message: <Input should be 'create_time' or 'update_time'> - Value: <pagerank>
2025-08-19 14:07:44.916 | ERROR    | 36462b495063443493d39f5f6d2148bd | 获取知识库列表失败: RAGFlow服务连接失败: 200: Field: <orderby> - Message: <Input should be 'create_time' or 'update_time'> - Value: <pagerank>
2025-08-19 14:07:48.272 | ERROR    | 919169ac1b3440639d2548af75abc062 | 获取知识库列表失败: RAGFlow服务连接失败: 200: Field: <orderby> - Message: <Input should be 'create_time' or 'update_time'> - Value: <pagerank>
2025-08-19 14:07:58.060 | ERROR    | 248fc6f43ba44d73906f110e3c860f49 | 获取知识库列表失败: RAGFlow服务连接失败: 200: Field: <orderby> - Message: <Input should be 'create_time' or 'update_time'> - Value: <pagerank>
2025-08-19 14:08:50.383 | ERROR    | f756e16074644d5280d308565316e1e1 | 获取知识库列表失败: RAGFlow服务连接失败: 200: Field: <orderby> - Message: <Input should be 'create_time' or 'update_time'> - Value: <pagerank>
2025-08-19 14:08:54.869 | ERROR    | ec94f0314d914024bb7d5ccb39988fdd | 获取知识库列表失败: RAGFlow服务连接失败: 200: Field: <orderby> - Message: <Input should be 'create_time' or 'update_time'> - Value: <pagerank>
2025-08-19 14:08:57.912 | ERROR    | 3e6c50bf781b41aea83d0354870dd54f | 获取知识库列表失败: RAGFlow服务连接失败: 200: Field: <orderby> - Message: <Input should be 'create_time' or 'update_time'> - Value: <pagerank>
2025-08-19 14:09:02.762 | ERROR    | 79cbf2a8899b4be1af18acaf998ebabc | 获取知识库列表失败: RAGFlow服务连接失败: 200: Field: <orderby> - Message: <Input should be 'create_time' or 'update_time'> - Value: <pagerank>
2025-08-19 14:10:08.030 | ERROR    | 41a56543fad347cea158bb148a602103 | 获取知识库列表失败: RAGFlow服务连接失败: 200: Field: <orderby> - Message: <Input should be 'create_time' or 'update_time'> - Value: <pagerank>
2025-08-19 14:10:10.726 | ERROR    | 245727d6bad541909ef8de242a18c57a | 获取知识库列表失败: RAGFlow服务连接失败: 200: Field: <orderby> - Message: <Input should be 'create_time' or 'update_time'> - Value: <pagerank>
2025-08-19 14:10:11.185 | ERROR    | e02e00a5cbb2481bbd97243a047659d8 | 获取知识库列表失败: RAGFlow服务连接失败: 200: Field: <orderby> - Message: <Input should be 'create_time' or 'update_time'> - Value: <pagerank>
2025-08-19 14:10:11.346 | ERROR    | 479bf27c384c430d95060c6e78aa7ccd | 获取知识库列表失败: RAGFlow服务连接失败: 200: Field: <orderby> - Message: <Input should be 'create_time' or 'update_time'> - Value: <pagerank>
2025-08-19 14:19:18.408 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x00000241195CA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x000002411BAF8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000002411BAFBA60>
    └ <uvicorn.server.Server object at 0x00000241228ED190>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002411BAFBB00>
           │       │   └ <uvicorn.server.Server object at 0x00000241228ED190>
           │       └ <function run at 0x000002411B2EF060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000241239EA880>
           │      └ <function Runner.run at 0x000002411B78B2E0>
           └ <asyncio.runners.Runner object at 0x00000241229DB4D0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002411B788EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000241229DB4D0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002411B858D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002411B78AC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002411B2E4860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1488, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 51455)>
    └ <_ProactorSocketTransport closing fd=1488>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。

2025-08-19 08:45:20.164 | INFO     | 04e00b670721482daccfc075be69746b | JWT标准验证成功，获取UUID: 95dcc3ab-4c7b-4dcc-9292-f289ec5bb5ca
2025-08-19 08:45:20.234 | INFO     | 04e00b670721482daccfc075be69746b | 成功认证Java用户: admin
2025-08-19 08:45:20.336 | INFO     | fc06be078da149c1a1c1ddc907d6e98a | JWT标准验证成功，获取UUID: 95dcc3ab-4c7b-4dcc-9292-f289ec5bb5ca
2025-08-19 08:45:20.338 | INFO     | fc06be078da149c1a1c1ddc907d6e98a | 成功认证Java用户: admin
2025-08-19 08:45:20.363 | INFO     | 04e00b670721482daccfc075be69746b | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 08:45:20.364 | INFO     | 04e00b670721482daccfc075be69746b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 202.745ms
2025-08-19 08:45:20.366 | INFO     | 257ad94dfb0d468d9a2cb6eacae1d3af | JWT标准验证成功，获取UUID: 95dcc3ab-4c7b-4dcc-9292-f289ec5bb5ca
2025-08-19 08:45:20.367 | INFO     | 257ad94dfb0d468d9a2cb6eacae1d3af | 成功认证Java用户: admin
2025-08-19 08:45:20.379 | INFO     | fc06be078da149c1a1c1ddc907d6e98a | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-19 08:45:20.379 | INFO     | fc06be078da149c1a1c1ddc907d6e98a | 权限检查通过: user_id=1, permission=knowledge:base:list
2025-08-19 08:45:20.385 | INFO     | 257ad94dfb0d468d9a2cb6eacae1d3af | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-19 08:45:20.385 | INFO     | 257ad94dfb0d468d9a2cb6eacae1d3af | 权限检查通过: user_id=1, permission=knowledge:base:stats
2025-08-19 08:45:20.395 | INFO     | fc06be078da149c1a1c1ddc907d6e98a | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 08:45:20.397 | INFO     | fc06be078da149c1a1c1ddc907d6e98a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 60.247ms
2025-08-19 08:45:20.402 | INFO     | 257ad94dfb0d468d9a2cb6eacae1d3af | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 08:45:20.403 | INFO     | 257ad94dfb0d468d9a2cb6eacae1d3af | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 37.105ms
2025-08-19 09:17:47.181 | INFO     | 7ce9179ef6c348509fdacb2d80530c53 | JWT标准验证成功，获取UUID: 95dcc3ab-4c7b-4dcc-9292-f289ec5bb5ca
2025-08-19 09:17:47.185 | INFO     | 7ce9179ef6c348509fdacb2d80530c53 | 成功认证Java用户: admin
2025-08-19 09:17:47.204 | INFO     | 7ce9179ef6c348509fdacb2d80530c53 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 09:17:47.205 | INFO     | 7ce9179ef6c348509fdacb2d80530c53 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 24.773ms
2025-08-19 09:17:47.207 | INFO     | 28f3c991db564c228868c9c122e9e18c | JWT标准验证成功，获取UUID: 95dcc3ab-4c7b-4dcc-9292-f289ec5bb5ca
2025-08-19 09:17:47.208 | INFO     | 28f3c991db564c228868c9c122e9e18c | 成功认证Java用户: admin
2025-08-19 09:17:47.213 | INFO     | 28f3c991db564c228868c9c122e9e18c | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-19 09:17:47.215 | INFO     | 28f3c991db564c228868c9c122e9e18c | 权限检查通过: user_id=1, permission=knowledge:base:list
2025-08-19 09:17:47.231 | INFO     | 28f3c991db564c228868c9c122e9e18c | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:17:47.233 | INFO     | 28f3c991db564c228868c9c122e9e18c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 26.194ms
2025-08-19 09:17:47.235 | INFO     | 48c4ba93cc094bca9643043d21da528c | JWT标准验证成功，获取UUID: 95dcc3ab-4c7b-4dcc-9292-f289ec5bb5ca
2025-08-19 09:17:47.236 | INFO     | 48c4ba93cc094bca9643043d21da528c | 成功认证Java用户: admin
2025-08-19 09:17:47.241 | INFO     | 48c4ba93cc094bca9643043d21da528c | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-19 09:17:47.242 | INFO     | 48c4ba93cc094bca9643043d21da528c | 权限检查通过: user_id=1, permission=knowledge:base:stats
2025-08-19 09:17:47.258 | INFO     | 48c4ba93cc094bca9643043d21da528c | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:17:47.260 | INFO     | 48c4ba93cc094bca9643043d21da528c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 25.069ms
2025-08-19 09:18:09.439 | INFO     | ee7439431baa48389216588e0723ce55 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:18:09.440 | INFO     | a8d46183c64347db821ba753617a4ec2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:18:09.441 | INFO     | b0c5da5e6f8242669ec8e550013366e7 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:18:09.442 | INFO     | ee7439431baa48389216588e0723ce55 | 成功认证Java用户: pythontest
2025-08-19 09:18:09.445 | INFO     | a8d46183c64347db821ba753617a4ec2 | 成功认证Java用户: pythontest
2025-08-19 09:18:09.445 | INFO     | b0c5da5e6f8242669ec8e550013366e7 | 成功认证Java用户: pythontest
2025-08-19 09:18:09.469 | INFO     | ee7439431baa48389216588e0723ce55 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 09:18:09.471 | INFO     | ee7439431baa48389216588e0723ce55 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 32.943ms
2025-08-19 09:18:09.497 | INFO     | b0c5da5e6f8242669ec8e550013366e7 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:18:09.498 | INFO     | b0c5da5e6f8242669ec8e550013366e7 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 09:18:09.503 | INFO     | a8d46183c64347db821ba753617a4ec2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:18:09.504 | INFO     | a8d46183c64347db821ba753617a4ec2 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 09:18:09.527 | INFO     | b0c5da5e6f8242669ec8e550013366e7 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:18:09.530 | INFO     | a8d46183c64347db821ba753617a4ec2 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:18:09.531 | INFO     | b0c5da5e6f8242669ec8e550013366e7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 90.624ms
2025-08-19 09:18:09.533 | INFO     | a8d46183c64347db821ba753617a4ec2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 93.464ms
2025-08-19 09:22:23.191 | INFO     | 1cd4dae1bc724c649421f630f686efb2 | 127.0.0.1       | GET      | 200    | /docs | 5.170ms
2025-08-19 09:22:23.308 | INFO     | fd9bffd9c60141029e0df1e3c89b705d | 127.0.0.1       | GET      | 200    | /openapi | 14.164ms
2025-08-19 09:24:42.851 | INFO     | 9dff88302df44db4a2fac28128b6e610 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:24:42.853 | INFO     | ad8c12543a024bd88a8fd17724ea9ab3 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:24:42.863 | INFO     | fc0e2c3045d44be794ef601d1070d42a | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:24:42.867 | INFO     | 9dff88302df44db4a2fac28128b6e610 | 成功认证Java用户: pythontest
2025-08-19 09:24:42.869 | INFO     | ad8c12543a024bd88a8fd17724ea9ab3 | 成功认证Java用户: pythontest
2025-08-19 09:24:42.871 | INFO     | fc0e2c3045d44be794ef601d1070d42a | 成功认证Java用户: pythontest
2025-08-19 09:24:42.886 | INFO     | fc0e2c3045d44be794ef601d1070d42a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:24:42.887 | INFO     | fc0e2c3045d44be794ef601d1070d42a | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 09:24:42.890 | INFO     | ad8c12543a024bd88a8fd17724ea9ab3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:24:42.891 | INFO     | ad8c12543a024bd88a8fd17724ea9ab3 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 09:24:42.898 | INFO     | 9dff88302df44db4a2fac28128b6e610 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 09:24:42.903 | INFO     | 9dff88302df44db4a2fac28128b6e610 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 51.918ms
2025-08-19 09:24:42.916 | INFO     | fc0e2c3045d44be794ef601d1070d42a | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:24:42.918 | INFO     | fc0e2c3045d44be794ef601d1070d42a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 54.869ms
2025-08-19 09:24:42.920 | INFO     | ad8c12543a024bd88a8fd17724ea9ab3 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:24:42.923 | INFO     | ad8c12543a024bd88a8fd17724ea9ab3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 70.000ms
2025-08-19 09:25:01.670 | INFO     | 1e0348e3f4754fa781af5b3b77e1959e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:25:01.671 | INFO     | 1e0348e3f4754fa781af5b3b77e1959e | 成功认证Java用户: pythontest
2025-08-19 09:25:01.707 | INFO     | 1e0348e3f4754fa781af5b3b77e1959e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:25:01.708 | INFO     | 1e0348e3f4754fa781af5b3b77e1959e | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-19 09:25:02.364 | INFO     | 1e0348e3f4754fa781af5b3b77e1959e | HTTP Request: DELETE http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 09:25:02.374 | INFO     | 1e0348e3f4754fa781af5b3b77e1959e | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 704.652ms
2025-08-19 09:25:02.383 | INFO     | db5bd342c9b44c8ca8536e389bc8f86e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:25:02.385 | INFO     | db5bd342c9b44c8ca8536e389bc8f86e | 成功认证Java用户: pythontest
2025-08-19 09:25:02.398 | INFO     | db5bd342c9b44c8ca8536e389bc8f86e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:25:02.399 | INFO     | db5bd342c9b44c8ca8536e389bc8f86e | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 09:25:02.416 | INFO     | db5bd342c9b44c8ca8536e389bc8f86e | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:25:02.417 | INFO     | db5bd342c9b44c8ca8536e389bc8f86e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 34.960ms
2025-08-19 09:25:02.420 | INFO     | ae08a180a54343978153a0d27e2e675a | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:25:02.422 | INFO     | ae08a180a54343978153a0d27e2e675a | 成功认证Java用户: pythontest
2025-08-19 09:25:02.428 | INFO     | ae08a180a54343978153a0d27e2e675a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:25:02.430 | INFO     | ae08a180a54343978153a0d27e2e675a | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 09:25:02.447 | INFO     | ae08a180a54343978153a0d27e2e675a | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:25:02.449 | INFO     | ae08a180a54343978153a0d27e2e675a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 28.404ms
2025-08-19 09:26:09.292 | INFO     | f8a08a47c6624a6683671069fb53678e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:26:09.294 | INFO     | f8a08a47c6624a6683671069fb53678e | 成功认证Java用户: pythontest
2025-08-19 09:26:09.302 | INFO     | f8a08a47c6624a6683671069fb53678e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:26:09.302 | INFO     | f8a08a47c6624a6683671069fb53678e | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-19 09:26:09.519 | INFO     | f8a08a47c6624a6683671069fb53678e | HTTP Request: DELETE http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 09:26:09.521 | INFO     | f8a08a47c6624a6683671069fb53678e | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 228.863ms
2025-08-19 09:26:09.530 | INFO     | 9abb184ab420433db7e751d2416c5ec9 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:26:09.532 | INFO     | 9abb184ab420433db7e751d2416c5ec9 | 成功认证Java用户: pythontest
2025-08-19 09:26:09.545 | INFO     | 9abb184ab420433db7e751d2416c5ec9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:26:09.546 | INFO     | 9abb184ab420433db7e751d2416c5ec9 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 09:26:09.559 | INFO     | d74cd1d51b6f4281b81435a0c90d264f | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:26:09.560 | INFO     | d74cd1d51b6f4281b81435a0c90d264f | 成功认证Java用户: pythontest
2025-08-19 09:26:09.563 | INFO     | 9abb184ab420433db7e751d2416c5ec9 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:26:09.566 | INFO     | 9abb184ab420433db7e751d2416c5ec9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 35.559ms
2025-08-19 09:26:09.572 | INFO     | d74cd1d51b6f4281b81435a0c90d264f | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:26:09.573 | INFO     | d74cd1d51b6f4281b81435a0c90d264f | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 09:26:09.591 | INFO     | d74cd1d51b6f4281b81435a0c90d264f | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:26:09.592 | INFO     | d74cd1d51b6f4281b81435a0c90d264f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 33.833ms
2025-08-19 09:26:13.018 | INFO     | 539d643fcfa240eb95978dcaaeb8b972 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:26:13.020 | INFO     | 539d643fcfa240eb95978dcaaeb8b972 | 成功认证Java用户: pythontest
2025-08-19 09:26:13.041 | INFO     | 539d643fcfa240eb95978dcaaeb8b972 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:26:13.042 | INFO     | 539d643fcfa240eb95978dcaaeb8b972 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 09:26:13.085 | INFO     | 539d643fcfa240eb95978dcaaeb8b972 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 09:26:13.091 | INFO     | 539d643fcfa240eb95978dcaaeb8b972 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base/create | 73.371ms
2025-08-19 09:26:15.589 | INFO     | e6e10b90040d42c79b6162b9e89ad3d2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:26:15.591 | INFO     | e6e10b90040d42c79b6162b9e89ad3d2 | 成功认证Java用户: pythontest
2025-08-19 09:26:15.600 | INFO     | e6e10b90040d42c79b6162b9e89ad3d2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:26:15.600 | INFO     | e6e10b90040d42c79b6162b9e89ad3d2 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 09:26:15.617 | INFO     | e6e10b90040d42c79b6162b9e89ad3d2 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 09:26:15.619 | INFO     | e6e10b90040d42c79b6162b9e89ad3d2 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base/create | 29.357ms
2025-08-19 09:26:16.401 | INFO     | 4f36690724b54637b253f556d65c0ab5 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:26:16.412 | INFO     | 4f36690724b54637b253f556d65c0ab5 | 成功认证Java用户: pythontest
2025-08-19 09:26:16.419 | INFO     | 4f36690724b54637b253f556d65c0ab5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:26:16.420 | INFO     | 4f36690724b54637b253f556d65c0ab5 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 09:26:16.437 | INFO     | 4f36690724b54637b253f556d65c0ab5 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 09:26:16.438 | INFO     | 4f36690724b54637b253f556d65c0ab5 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base/create | 38.145ms
2025-08-19 09:26:16.882 | INFO     | 1454eefb1cc641b29c5dbb570bc7d150 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:26:16.884 | INFO     | 1454eefb1cc641b29c5dbb570bc7d150 | 成功认证Java用户: pythontest
2025-08-19 09:26:16.893 | INFO     | 1454eefb1cc641b29c5dbb570bc7d150 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:26:16.894 | INFO     | 1454eefb1cc641b29c5dbb570bc7d150 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 09:26:16.912 | INFO     | 1454eefb1cc641b29c5dbb570bc7d150 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 09:26:16.914 | INFO     | 1454eefb1cc641b29c5dbb570bc7d150 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base/create | 32.170ms
2025-08-19 09:26:17.320 | INFO     | 9611f50408ee49f3967dfa6df77697c8 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:26:17.322 | INFO     | 9611f50408ee49f3967dfa6df77697c8 | 成功认证Java用户: pythontest
2025-08-19 09:26:17.353 | INFO     | 9611f50408ee49f3967dfa6df77697c8 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:26:17.354 | INFO     | 9611f50408ee49f3967dfa6df77697c8 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 09:26:17.369 | INFO     | 9611f50408ee49f3967dfa6df77697c8 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 09:26:17.371 | INFO     | 9611f50408ee49f3967dfa6df77697c8 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base/create | 50.312ms
2025-08-19 09:26:17.699 | INFO     | e8cad2c4ff9f4367a890ea6aa67d9d03 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:26:17.701 | INFO     | e8cad2c4ff9f4367a890ea6aa67d9d03 | 成功认证Java用户: pythontest
2025-08-19 09:26:17.708 | INFO     | e8cad2c4ff9f4367a890ea6aa67d9d03 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:26:17.708 | INFO     | e8cad2c4ff9f4367a890ea6aa67d9d03 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 09:26:17.724 | INFO     | e8cad2c4ff9f4367a890ea6aa67d9d03 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 09:26:17.726 | INFO     | e8cad2c4ff9f4367a890ea6aa67d9d03 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base/create | 27.770ms
2025-08-19 09:26:18.062 | INFO     | 6de0c78153c647109c047ef0f773ebb8 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:26:18.063 | INFO     | 6de0c78153c647109c047ef0f773ebb8 | 成功认证Java用户: pythontest
2025-08-19 09:26:18.071 | INFO     | 6de0c78153c647109c047ef0f773ebb8 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:26:18.072 | INFO     | 6de0c78153c647109c047ef0f773ebb8 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 09:26:18.088 | INFO     | 6de0c78153c647109c047ef0f773ebb8 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 09:26:18.090 | INFO     | 6de0c78153c647109c047ef0f773ebb8 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base/create | 28.696ms
2025-08-19 09:34:53.194 | INFO     | 46160b1402724e7e963afbd5a469342c | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:34:53.198 | INFO     | 46160b1402724e7e963afbd5a469342c | 成功认证Java用户: pythontest
2025-08-19 09:34:53.216 | INFO     | 46160b1402724e7e963afbd5a469342c | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 09:34:53.218 | INFO     | 46160b1402724e7e963afbd5a469342c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 23.621ms
2025-08-19 09:34:53.221 | INFO     | e94e28707cbd465494f0a7ec1012bc05 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:34:53.223 | INFO     | e94e28707cbd465494f0a7ec1012bc05 | 成功认证Java用户: pythontest
2025-08-19 09:34:53.229 | INFO     | e94e28707cbd465494f0a7ec1012bc05 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:34:53.230 | INFO     | e94e28707cbd465494f0a7ec1012bc05 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 09:34:53.245 | INFO     | e94e28707cbd465494f0a7ec1012bc05 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:34:53.247 | INFO     | e94e28707cbd465494f0a7ec1012bc05 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 26.215ms
2025-08-19 09:34:53.250 | INFO     | 412610bb56c747019bbd48f5f109217b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:34:53.251 | INFO     | 412610bb56c747019bbd48f5f109217b | 成功认证Java用户: pythontest
2025-08-19 09:34:53.257 | INFO     | 412610bb56c747019bbd48f5f109217b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:34:53.257 | INFO     | 412610bb56c747019bbd48f5f109217b | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 09:34:53.272 | INFO     | 412610bb56c747019bbd48f5f109217b | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:34:53.275 | INFO     | 412610bb56c747019bbd48f5f109217b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 24.890ms
2025-08-19 09:34:55.159 | INFO     | a29207b4b8b54595ab2230df716f7fe7 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:34:55.160 | INFO     | 7f9cf82a82e348adba81e70e74fe42c6 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:34:55.161 | INFO     | e8b08e9806d84df09ba93e41a5f473cc | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:34:55.162 | INFO     | a29207b4b8b54595ab2230df716f7fe7 | 成功认证Java用户: pythontest
2025-08-19 09:34:55.163 | INFO     | 7f9cf82a82e348adba81e70e74fe42c6 | 成功认证Java用户: pythontest
2025-08-19 09:34:55.163 | INFO     | e8b08e9806d84df09ba93e41a5f473cc | 成功认证Java用户: pythontest
2025-08-19 09:34:55.174 | INFO     | e8b08e9806d84df09ba93e41a5f473cc | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:34:55.174 | INFO     | e8b08e9806d84df09ba93e41a5f473cc | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 09:34:55.180 | INFO     | 7f9cf82a82e348adba81e70e74fe42c6 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:34:55.181 | INFO     | 7f9cf82a82e348adba81e70e74fe42c6 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 09:34:55.185 | INFO     | a29207b4b8b54595ab2230df716f7fe7 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 09:34:55.190 | INFO     | a29207b4b8b54595ab2230df716f7fe7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 31.224ms
2025-08-19 09:34:55.205 | INFO     | e8b08e9806d84df09ba93e41a5f473cc | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:34:55.206 | INFO     | 7f9cf82a82e348adba81e70e74fe42c6 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:34:55.209 | INFO     | e8b08e9806d84df09ba93e41a5f473cc | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 49.339ms
2025-08-19 09:34:55.211 | INFO     | 7f9cf82a82e348adba81e70e74fe42c6 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 51.579ms
2025-08-19 09:51:14.304 | INFO     | b3732553d4834793a2e5634706509557 | 127.0.0.1       | GET      | 200    | /docs | 1.694ms
2025-08-19 09:51:14.414 | INFO     | f5f3128424f74a35ad94dd3518e15fcc | 127.0.0.1       | GET      | 200    | /openapi | 2.734ms
2025-08-19 09:53:52.408 | INFO     | d81ef22231c24c9a9cf58c699643d018 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:53:52.409 | INFO     | e4264c7880f24b70946cdf4452f8877e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:53:52.410 | INFO     | d81ef22231c24c9a9cf58c699643d018 | 成功认证Java用户: pythontest
2025-08-19 09:53:52.411 | INFO     | e4264c7880f24b70946cdf4452f8877e | 成功认证Java用户: pythontest
2025-08-19 09:53:52.428 | INFO     | d81ef22231c24c9a9cf58c699643d018 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 09:53:52.429 | INFO     | d81ef22231c24c9a9cf58c699643d018 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 21.036ms
2025-08-19 09:53:52.430 | INFO     | e4264c7880f24b70946cdf4452f8877e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:53:52.431 | INFO     | e4264c7880f24b70946cdf4452f8877e | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 09:53:52.434 | INFO     | 309e61e69efd4840a3b16bd19dc7e97f | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:53:52.436 | INFO     | 309e61e69efd4840a3b16bd19dc7e97f | 成功认证Java用户: pythontest
2025-08-19 09:53:52.447 | INFO     | e4264c7880f24b70946cdf4452f8877e | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:53:52.448 | INFO     | e4264c7880f24b70946cdf4452f8877e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 39.408ms
2025-08-19 09:53:52.451 | INFO     | 309e61e69efd4840a3b16bd19dc7e97f | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:53:52.452 | INFO     | 309e61e69efd4840a3b16bd19dc7e97f | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 09:53:52.468 | INFO     | 309e61e69efd4840a3b16bd19dc7e97f | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:53:52.469 | INFO     | 309e61e69efd4840a3b16bd19dc7e97f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 35.242ms
2025-08-19 09:54:02.741 | INFO     | 96c5fd0788ad4c32b4c7621259f329db | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:54:02.742 | INFO     | 37dc68e23fc849e0824fc1eb01388181 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:54:02.743 | INFO     | c53cab3a96df4e46919e66a937064908 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:54:02.743 | INFO     | 96c5fd0788ad4c32b4c7621259f329db | 成功认证Java用户: pythontest
2025-08-19 09:54:02.744 | INFO     | 37dc68e23fc849e0824fc1eb01388181 | 成功认证Java用户: pythontest
2025-08-19 09:54:02.745 | INFO     | c53cab3a96df4e46919e66a937064908 | 成功认证Java用户: pythontest
2025-08-19 09:54:02.756 | INFO     | c53cab3a96df4e46919e66a937064908 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:54:02.757 | INFO     | c53cab3a96df4e46919e66a937064908 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 09:54:02.760 | INFO     | 37dc68e23fc849e0824fc1eb01388181 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:54:02.761 | INFO     | 37dc68e23fc849e0824fc1eb01388181 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 09:54:02.764 | INFO     | 96c5fd0788ad4c32b4c7621259f329db | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 09:54:02.766 | INFO     | 96c5fd0788ad4c32b4c7621259f329db | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 25.040ms
2025-08-19 09:54:02.781 | INFO     | c53cab3a96df4e46919e66a937064908 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:54:02.781 | INFO     | 37dc68e23fc849e0824fc1eb01388181 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:54:02.783 | INFO     | c53cab3a96df4e46919e66a937064908 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 40.261ms
2025-08-19 09:54:02.784 | INFO     | 37dc68e23fc849e0824fc1eb01388181 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 41.640ms
2025-08-19 09:54:14.748 | INFO     | a9412d5a0405487a81e27091da68ba37 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:54:14.749 | INFO     | 4c6707431ce74c6686e14246a017501e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:54:14.749 | INFO     | cb54a70e95ae4bac83ccc3b6a3e6de78 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:54:14.750 | INFO     | a9412d5a0405487a81e27091da68ba37 | 成功认证Java用户: pythontest
2025-08-19 09:54:14.750 | INFO     | 4c6707431ce74c6686e14246a017501e | 成功认证Java用户: pythontest
2025-08-19 09:54:14.751 | INFO     | cb54a70e95ae4bac83ccc3b6a3e6de78 | 成功认证Java用户: pythontest
2025-08-19 09:54:14.759 | INFO     | cb54a70e95ae4bac83ccc3b6a3e6de78 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:54:14.760 | INFO     | cb54a70e95ae4bac83ccc3b6a3e6de78 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 09:54:14.764 | INFO     | 4c6707431ce74c6686e14246a017501e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:54:14.764 | INFO     | 4c6707431ce74c6686e14246a017501e | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 09:54:14.767 | INFO     | a9412d5a0405487a81e27091da68ba37 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 09:54:14.772 | INFO     | a9412d5a0405487a81e27091da68ba37 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 23.874ms
2025-08-19 09:54:14.786 | INFO     | cb54a70e95ae4bac83ccc3b6a3e6de78 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:54:14.787 | INFO     | 4c6707431ce74c6686e14246a017501e | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:54:14.789 | INFO     | cb54a70e95ae4bac83ccc3b6a3e6de78 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 40.046ms
2025-08-19 09:54:14.790 | INFO     | 4c6707431ce74c6686e14246a017501e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 41.062ms
2025-08-19 09:55:03.695 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-19 09:55:30.209 | INFO     | 13eb16759a624c1191d4a412839776bd | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:55:30.212 | INFO     | 13eb16759a624c1191d4a412839776bd | 成功认证Java用户: pythontest
2025-08-19 09:55:30.238 | INFO     | 13eb16759a624c1191d4a412839776bd | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:55:30.239 | INFO     | 13eb16759a624c1191d4a412839776bd | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 09:55:30.283 | INFO     | 13eb16759a624c1191d4a412839776bd | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 09:55:30.286 | INFO     | 13eb16759a624c1191d4a412839776bd | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 87.159ms
2025-08-19 09:55:33.647 | INFO     | e940de79087346ed9c25cee46dca8bd0 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:55:33.650 | INFO     | e940de79087346ed9c25cee46dca8bd0 | 成功认证Java用户: pythontest
2025-08-19 09:55:33.672 | INFO     | e940de79087346ed9c25cee46dca8bd0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:55:33.674 | INFO     | e940de79087346ed9c25cee46dca8bd0 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 09:55:33.690 | INFO     | e940de79087346ed9c25cee46dca8bd0 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 09:55:33.691 | INFO     | e940de79087346ed9c25cee46dca8bd0 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 44.011ms
2025-08-19 09:57:30.860 | INFO     | ef34fdd0987a4bd6997c1c0c70f99672 | 127.0.0.1       | GET      | 404    | / | 4.578ms
2025-08-19 09:57:30.868 | INFO     | acd14f4ccd24456c97b1c6b1f3c36138 | 127.0.0.1       | GET      | 200    | /docs | 0.927ms
2025-08-19 09:57:31.255 | INFO     | bcd3265291f84c3481ee227df1d68e68 | 127.0.0.1       | GET      | 404    | /openapi.json | 1.241ms
2025-08-19 09:57:31.668 | INFO     | 8c21b9353197482badebcde8a17ed3d1 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 09:57:31.669 | INFO     | 8c21b9353197482badebcde8a17ed3d1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 21.165ms
2025-08-19 09:58:36.901 | INFO     | be84aaeed52742d591b07e03679cbb8c | 127.0.0.1       | GET      | 404    | / | 1.587ms
2025-08-19 09:58:36.910 | INFO     | 7c09858b8ff944ea820c146985afe6d2 | 127.0.0.1       | GET      | 200    | /docs | 1.253ms
2025-08-19 09:58:37.319 | INFO     | 95d22fc02ba946c1af75e031a677b456 | 127.0.0.1       | GET      | 404    | /openapi.json | 1.331ms
2025-08-19 09:58:37.723 | INFO     | e6e4f468cfda4e899bb9a4465f7e9648 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 09:58:37.724 | INFO     | e6e4f468cfda4e899bb9a4465f7e9648 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 13.484ms
2025-08-19 10:00:59.506 | INFO     | 90f54831e7104fb6b8371e7f80e3f3c4 | JWT标准验证成功，获取UUID: fdf903e2-f721-4db1-9617-0be331871b9c
2025-08-19 10:00:59.506 | INFO     | c380c088aa2646fcbc5f1bfb35745d6d | JWT标准验证成功，获取UUID: fdf903e2-f721-4db1-9617-0be331871b9c
2025-08-19 10:00:59.509 | INFO     | d9a6ab7fafe34f1a86fd6c5e924a3dbf | JWT标准验证成功，获取UUID: fdf903e2-f721-4db1-9617-0be331871b9c
2025-08-19 10:00:59.516 | INFO     | 90f54831e7104fb6b8371e7f80e3f3c4 | 成功认证Java用户: admin
2025-08-19 10:00:59.519 | INFO     | c380c088aa2646fcbc5f1bfb35745d6d | 成功认证Java用户: admin
2025-08-19 10:00:59.520 | INFO     | d9a6ab7fafe34f1a86fd6c5e924a3dbf | 成功认证Java用户: admin
2025-08-19 10:00:59.535 | INFO     | d9a6ab7fafe34f1a86fd6c5e924a3dbf | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-19 10:00:59.535 | INFO     | d9a6ab7fafe34f1a86fd6c5e924a3dbf | 权限检查通过: user_id=1, permission=knowledge:base:stats
2025-08-19 10:00:59.544 | INFO     | 90f54831e7104fb6b8371e7f80e3f3c4 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 10:00:59.546 | INFO     | c380c088aa2646fcbc5f1bfb35745d6d | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-19 10:00:59.547 | INFO     | c380c088aa2646fcbc5f1bfb35745d6d | 权限检查通过: user_id=1, permission=knowledge:base:list
2025-08-19 10:00:59.551 | INFO     | 90f54831e7104fb6b8371e7f80e3f3c4 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 45.916ms
2025-08-19 10:00:59.553 | INFO     | d9a6ab7fafe34f1a86fd6c5e924a3dbf | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 10:00:59.556 | INFO     | d9a6ab7fafe34f1a86fd6c5e924a3dbf | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 48.472ms
2025-08-19 10:00:59.566 | INFO     | c380c088aa2646fcbc5f1bfb35745d6d | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 10:00:59.568 | INFO     | c380c088aa2646fcbc5f1bfb35745d6d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 61.852ms
2025-08-19 10:03:00.695 | INFO     | 68ad47398d0c4d3bbb89fc73e859d0bb | 127.0.0.1       | GET      | 200    | /docs | 1.824ms
2025-08-19 10:03:00.836 | INFO     | bff676658400494c8dcbb987441cf30a | 127.0.0.1       | GET      | 200    | /openapi | 73.428ms
2025-08-19 10:04:28.252 | INFO     | b2c8cdd7ea1a44438ea7badff86baea7 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 10:04:28.253 | INFO     | b2c8cdd7ea1a44438ea7badff86baea7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 14.128ms
2025-08-19 10:04:28.262 | INFO     | 7e8aec06610e4139b3fdffca0f449f74 | 127.0.0.1       | POST     | 401    | /api/iot/v1/knowledge-base | 1.685ms
2025-08-19 10:04:28.272 | INFO     | 4ca54b44405f4e1c93c11ed753fa1365 | 127.0.0.1       | POST     | 401    | /api/iot/v1/knowledge-base/create | 2.296ms
2025-08-19 10:04:28.281 | INFO     | 49894b4fb7314dd698ad298d903eeb5d | 127.0.0.1       | GET      | 401    | /api/iot/v1/knowledge-base/list | 1.434ms
2025-08-19 10:04:28.289 | INFO     | 37a914aa425f46b1aec5a9567f571ba2 | 127.0.0.1       | GET      | 401    | /api/iot/v1/knowledge-base/stats/overview | 0.994ms
2025-08-19 10:04:28.671 | INFO     | d1292390353d4ed892e33847aeffc6ae | 127.0.0.1       | POST     | 401    | /api/iot/v1/knowledge-base | 1.888ms
2025-08-19 10:04:28.679 | INFO     | fe1d180657cd4b1585f5658466fdd6c3 | 127.0.0.1       | POST     | 401    | /api/iot/v1/knowledge-base | 1.656ms
2025-08-19 10:04:28.688 | INFO     | 2417a72c84034d239ecec0db0268c372 | 127.0.0.1       | POST     | 401    | /api/iot/v1/knowledge-base | 2.035ms
2025-08-19 10:04:28.697 | INFO     | c78a3a13e4564abaadbb9af0c554738b | 127.0.0.1       | POST     | 401    | /api/iot/v1/knowledge-base | 1.836ms
2025-08-19 10:04:29.089 | INFO     | 74e29af6f9f14ee8996ffba7f73d01fc | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 10:04:29.090 | INFO     | 74e29af6f9f14ee8996ffba7f73d01fc | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 13.290ms
2025-08-19 10:08:03.743 | INFO     | a19549a6c22244b2afc5c9786e267e15 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:08:03.745 | INFO     | a19549a6c22244b2afc5c9786e267e15 | 成功认证Java用户: pythontest
2025-08-19 10:08:03.753 | INFO     | a19549a6c22244b2afc5c9786e267e15 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 10:08:03.754 | INFO     | a19549a6c22244b2afc5c9786e267e15 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 10:08:03.772 | INFO     | a19549a6c22244b2afc5c9786e267e15 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 10:08:03.773 | INFO     | a19549a6c22244b2afc5c9786e267e15 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 31.481ms
2025-08-19 10:09:35.797 | INFO     | 8ebdd270f1a24ef585e65f63090926ef | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:09:35.798 | INFO     | a6e69780a9b845d2ad274a83cf2c584c | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:09:35.798 | INFO     | 113cb6daa34547da9edf68a8d0f3f82a | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:09:35.801 | INFO     | 8ebdd270f1a24ef585e65f63090926ef | 成功认证Java用户: pythontest
2025-08-19 10:09:35.802 | INFO     | a6e69780a9b845d2ad274a83cf2c584c | 成功认证Java用户: pythontest
2025-08-19 10:09:35.802 | INFO     | 113cb6daa34547da9edf68a8d0f3f82a | 成功认证Java用户: pythontest
2025-08-19 10:09:35.811 | INFO     | a6e69780a9b845d2ad274a83cf2c584c | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 10:09:35.811 | INFO     | a6e69780a9b845d2ad274a83cf2c584c | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 10:09:35.813 | INFO     | 113cb6daa34547da9edf68a8d0f3f82a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 10:09:35.814 | INFO     | 113cb6daa34547da9edf68a8d0f3f82a | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 10:09:35.818 | INFO     | 8ebdd270f1a24ef585e65f63090926ef | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 10:09:35.820 | INFO     | 8ebdd270f1a24ef585e65f63090926ef | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 22.055ms
2025-08-19 10:09:35.833 | INFO     | a6e69780a9b845d2ad274a83cf2c584c | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 10:09:35.835 | INFO     | 113cb6daa34547da9edf68a8d0f3f82a | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 10:09:35.835 | INFO     | a6e69780a9b845d2ad274a83cf2c584c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 37.543ms
2025-08-19 10:09:35.836 | INFO     | 113cb6daa34547da9edf68a8d0f3f82a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 37.658ms
2025-08-19 10:10:02.028 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-19 10:10:11.592 | INFO     | f66faea702b8491cb9e1c554956ac068 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:10:11.594 | INFO     | f66faea702b8491cb9e1c554956ac068 | 成功认证Java用户: pythontest
2025-08-19 10:10:11.616 | INFO     | f66faea702b8491cb9e1c554956ac068 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 10:10:11.617 | INFO     | f66faea702b8491cb9e1c554956ac068 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 10:10:11.652 | INFO     | f66faea702b8491cb9e1c554956ac068 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 10:10:11.655 | INFO     | f66faea702b8491cb9e1c554956ac068 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 67.152ms
2025-08-19 10:14:19.031 | INFO     | 4c7a74df74234740991b0e94d93a00e7 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:14:19.033 | INFO     | 4c7a74df74234740991b0e94d93a00e7 | 成功认证Java用户: pythontest
2025-08-19 10:14:19.047 | INFO     | 75470ce3df284e95a9941b4a50d5544a | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:14:19.047 | INFO     | 404755bc715d4c0a9ccc661950728fbc | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:14:19.048 | INFO     | 4c7a74df74234740991b0e94d93a00e7 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 10:14:19.049 | INFO     | 75470ce3df284e95a9941b4a50d5544a | 成功认证Java用户: pythontest
2025-08-19 10:14:19.049 | INFO     | 4c7a74df74234740991b0e94d93a00e7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 19.143ms
2025-08-19 10:14:19.052 | INFO     | 404755bc715d4c0a9ccc661950728fbc | 成功认证Java用户: pythontest
2025-08-19 10:14:19.072 | INFO     | 404755bc715d4c0a9ccc661950728fbc | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 10:14:19.072 | INFO     | 404755bc715d4c0a9ccc661950728fbc | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 10:14:19.083 | INFO     | 75470ce3df284e95a9941b4a50d5544a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 10:14:19.084 | INFO     | 75470ce3df284e95a9941b4a50d5544a | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 10:14:19.087 | INFO     | 404755bc715d4c0a9ccc661950728fbc | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 10:14:19.088 | INFO     | 404755bc715d4c0a9ccc661950728fbc | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 40.702ms
2025-08-19 10:14:19.098 | INFO     | 75470ce3df284e95a9941b4a50d5544a | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 10:14:19.099 | INFO     | 75470ce3df284e95a9941b4a50d5544a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 52.420ms
2025-08-19 10:18:02.036 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-19 10:18:12.560 | INFO     | 1b269c803ffe44e7a3425340bc7a9b5c | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:18:12.561 | INFO     | afe7c9198a864ca49568689bdcf72dc1 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:18:12.563 | INFO     | c8120564228d4d029f3a2ffc268b1b97 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:18:12.563 | INFO     | 1b269c803ffe44e7a3425340bc7a9b5c | 成功认证Java用户: pythontest
2025-08-19 10:18:12.566 | INFO     | afe7c9198a864ca49568689bdcf72dc1 | 成功认证Java用户: pythontest
2025-08-19 10:18:12.566 | INFO     | c8120564228d4d029f3a2ffc268b1b97 | 成功认证Java用户: pythontest
2025-08-19 10:18:12.594 | INFO     | c8120564228d4d029f3a2ffc268b1b97 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 10:18:12.594 | INFO     | c8120564228d4d029f3a2ffc268b1b97 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 10:18:12.597 | INFO     | afe7c9198a864ca49568689bdcf72dc1 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 10:18:12.598 | INFO     | afe7c9198a864ca49568689bdcf72dc1 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 10:18:12.599 | INFO     | 1b269c803ffe44e7a3425340bc7a9b5c | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 10:18:12.602 | INFO     | 1b269c803ffe44e7a3425340bc7a9b5c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 45.436ms
2025-08-19 10:18:12.618 | INFO     | c8120564228d4d029f3a2ffc268b1b97 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 10:18:12.619 | INFO     | afe7c9198a864ca49568689bdcf72dc1 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 10:18:12.620 | INFO     | c8120564228d4d029f3a2ffc268b1b97 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 59.431ms
2025-08-19 10:18:12.621 | INFO     | afe7c9198a864ca49568689bdcf72dc1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 60.531ms
2025-08-19 10:18:20.542 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:18:20.544 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | 成功认证Java用户: pythontest
2025-08-19 10:18:20.561 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 10:18:20.562 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 10:18:20.562 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | 创建知识库请求数据: {'name': 'pythontest3', 'description': '', 'embedding_model': 'bge-m3:latest@Ollama', 'permission': 'me', 'chunk_method': 'naive', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'auto_keywords': 0, 'auto_questions': 0, 'task_page_size': 12}}
2025-08-19 10:18:20.579 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 10:18:20.580 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | 尝试使用备用模型: BAAI/bge-zh-v1.5
2025-08-19 10:18:20.593 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 10:18:20.594 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | 尝试使用备用模型: BAAI/bge-large-zh-v1.5
2025-08-19 10:18:20.605 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 10:18:20.606 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | 尝试使用备用模型: text-embedding-ada-002
2025-08-19 10:18:20.617 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 10:18:20.618 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 77.087ms
2025-08-19 11:37:17.149 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-19 11:37:40.228 | INFO     | f36e48582d2b4f7c9c426103078ca456 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:37:40.230 | INFO     | a329fadeb4604146b034da10960c1c8d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:37:40.231 | INFO     | f36e48582d2b4f7c9c426103078ca456 | 成功认证Java用户: pythontest
2025-08-19 11:37:40.233 | INFO     | a329fadeb4604146b034da10960c1c8d | 成功认证Java用户: pythontest
2025-08-19 11:37:40.312 | INFO     | f36e48582d2b4f7c9c426103078ca456 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 11:37:40.314 | INFO     | f36e48582d2b4f7c9c426103078ca456 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 93.387ms
2025-08-19 11:37:40.316 | INFO     | a329fadeb4604146b034da10960c1c8d | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:37:40.317 | INFO     | a329fadeb4604146b034da10960c1c8d | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:37:40.322 | INFO     | f02fcf8e76eb455884838242cacdcaeb | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:37:40.324 | INFO     | f02fcf8e76eb455884838242cacdcaeb | 成功认证Java用户: pythontest
2025-08-19 11:37:40.343 | INFO     | a329fadeb4604146b034da10960c1c8d | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:37:40.348 | INFO     | a329fadeb4604146b034da10960c1c8d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 117.754ms
2025-08-19 11:37:40.351 | INFO     | f02fcf8e76eb455884838242cacdcaeb | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:37:40.352 | INFO     | f02fcf8e76eb455884838242cacdcaeb | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:37:40.369 | INFO     | f02fcf8e76eb455884838242cacdcaeb | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:37:40.372 | INFO     | f02fcf8e76eb455884838242cacdcaeb | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 50.036ms
2025-08-19 11:37:44.760 | INFO     | ba10d882517b49079a7caf863f7ce557 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:37:44.761 | INFO     | ba10d882517b49079a7caf863f7ce557 | 成功认证Java用户: pythontest
2025-08-19 11:37:44.772 | INFO     | ba10d882517b49079a7caf863f7ce557 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:37:44.773 | INFO     | ba10d882517b49079a7caf863f7ce557 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 11:37:44.774 | INFO     | ba10d882517b49079a7caf863f7ce557 | 创建知识库请求数据: {'name': 'pythontest3', 'description': '', 'embedding_model': 'bge-m3:latest@Ollama', 'permission': 'me', 'chunk_method': 'naive', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'auto_keywords': 0, 'auto_questions': 0, 'task_page_size': 12}}
2025-08-19 11:37:44.796 | INFO     | ba10d882517b49079a7caf863f7ce557 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:37:44.798 | INFO     | ba10d882517b49079a7caf863f7ce557 | 尝试使用备用模型: BAAI/bge-zh-v1.5
2025-08-19 11:37:44.811 | INFO     | ba10d882517b49079a7caf863f7ce557 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:37:44.813 | INFO     | ba10d882517b49079a7caf863f7ce557 | 尝试使用备用模型: BAAI/bge-large-zh-v1.5
2025-08-19 11:37:44.828 | INFO     | ba10d882517b49079a7caf863f7ce557 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:37:44.830 | INFO     | ba10d882517b49079a7caf863f7ce557 | 尝试使用备用模型: text-embedding-ada-002
2025-08-19 11:37:44.844 | INFO     | ba10d882517b49079a7caf863f7ce557 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:37:44.847 | INFO     | ba10d882517b49079a7caf863f7ce557 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 87.167ms
2025-08-19 11:39:25.984 | INFO     | 697d04c6295e4a18ab179af9f58fa6a2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:39:25.986 | INFO     | 697d04c6295e4a18ab179af9f58fa6a2 | 成功认证Java用户: pythontest
2025-08-19 11:39:25.991 | INFO     | 697d04c6295e4a18ab179af9f58fa6a2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:39:25.991 | INFO     | 697d04c6295e4a18ab179af9f58fa6a2 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:39:26.007 | INFO     | 697d04c6295e4a18ab179af9f58fa6a2 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:39:26.009 | INFO     | 697d04c6295e4a18ab179af9f58fa6a2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 25.479ms
2025-08-19 11:39:37.361 | INFO     | 1d437b9cab264b9eb3e8d42bf8482548 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:39:37.362 | INFO     | 1d437b9cab264b9eb3e8d42bf8482548 | 成功认证Java用户: pythontest
2025-08-19 11:39:37.380 | INFO     | 1d437b9cab264b9eb3e8d42bf8482548 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 11:39:37.382 | INFO     | 1d437b9cab264b9eb3e8d42bf8482548 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 21.119ms
2025-08-19 11:39:37.384 | INFO     | 3a09174156744f71bbf9623784688c36 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:39:37.385 | INFO     | 3a09174156744f71bbf9623784688c36 | 成功认证Java用户: pythontest
2025-08-19 11:39:37.392 | INFO     | 3a09174156744f71bbf9623784688c36 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:39:37.392 | INFO     | 3a09174156744f71bbf9623784688c36 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:39:37.408 | INFO     | 3a09174156744f71bbf9623784688c36 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:39:37.410 | INFO     | 3a09174156744f71bbf9623784688c36 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 26.403ms
2025-08-19 11:39:37.412 | INFO     | 2f300af1489f4c5991ce4f14b7c7d323 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:39:37.413 | INFO     | 2f300af1489f4c5991ce4f14b7c7d323 | 成功认证Java用户: pythontest
2025-08-19 11:39:37.419 | INFO     | 2f300af1489f4c5991ce4f14b7c7d323 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:39:37.419 | INFO     | 2f300af1489f4c5991ce4f14b7c7d323 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:39:37.436 | INFO     | 2f300af1489f4c5991ce4f14b7c7d323 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:39:37.438 | INFO     | 2f300af1489f4c5991ce4f14b7c7d323 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 25.887ms
2025-08-19 11:41:20.622 | INFO     | 0b1bffe2eed84ac7887b9e2c0a314f7b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:41:20.623 | INFO     | 36294cee3bd14ecbb153d2fa9e1266a0 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:41:20.623 | INFO     | acbd52535d6a494093ed3580973b2b2b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:41:20.624 | INFO     | 0b1bffe2eed84ac7887b9e2c0a314f7b | 成功认证Java用户: pythontest
2025-08-19 11:41:20.624 | INFO     | 36294cee3bd14ecbb153d2fa9e1266a0 | 成功认证Java用户: pythontest
2025-08-19 11:41:20.627 | INFO     | acbd52535d6a494093ed3580973b2b2b | 成功认证Java用户: pythontest
2025-08-19 11:41:20.634 | INFO     | acbd52535d6a494093ed3580973b2b2b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:41:20.635 | INFO     | acbd52535d6a494093ed3580973b2b2b | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:41:20.643 | INFO     | 36294cee3bd14ecbb153d2fa9e1266a0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:41:20.644 | INFO     | 36294cee3bd14ecbb153d2fa9e1266a0 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:41:20.647 | INFO     | 0b1bffe2eed84ac7887b9e2c0a314f7b | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 11:41:20.649 | INFO     | 0b1bffe2eed84ac7887b9e2c0a314f7b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 27.762ms
2025-08-19 11:41:20.652 | INFO     | acbd52535d6a494093ed3580973b2b2b | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:41:20.653 | INFO     | acbd52535d6a494093ed3580973b2b2b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 30.156ms
2025-08-19 11:41:20.662 | INFO     | 36294cee3bd14ecbb153d2fa9e1266a0 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:41:20.664 | INFO     | 36294cee3bd14ecbb153d2fa9e1266a0 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 41.360ms
2025-08-19 11:46:14.662 | INFO     | c3846d9a6ef34166849faec850bd6feb | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:46:14.662 | INFO     | 7de866907eb2420594dbf936f51e6f77 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:46:14.663 | INFO     | 7c0b3a484a794719b2a5777ddd516980 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:46:14.664 | INFO     | c3846d9a6ef34166849faec850bd6feb | 成功认证Java用户: pythontest
2025-08-19 11:46:14.665 | INFO     | 7de866907eb2420594dbf936f51e6f77 | 成功认证Java用户: pythontest
2025-08-19 11:46:14.665 | INFO     | 7c0b3a484a794719b2a5777ddd516980 | 成功认证Java用户: pythontest
2025-08-19 11:46:14.673 | INFO     | 7c0b3a484a794719b2a5777ddd516980 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:46:14.674 | INFO     | 7c0b3a484a794719b2a5777ddd516980 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:46:14.676 | INFO     | 7de866907eb2420594dbf936f51e6f77 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:46:14.677 | INFO     | 7de866907eb2420594dbf936f51e6f77 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:46:14.683 | INFO     | c3846d9a6ef34166849faec850bd6feb | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 11:46:14.684 | INFO     | c3846d9a6ef34166849faec850bd6feb | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 23.196ms
2025-08-19 11:46:14.698 | INFO     | 7c0b3a484a794719b2a5777ddd516980 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:46:14.700 | INFO     | 7c0b3a484a794719b2a5777ddd516980 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 37.197ms
2025-08-19 11:46:14.702 | INFO     | 7de866907eb2420594dbf936f51e6f77 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:46:14.703 | INFO     | 7de866907eb2420594dbf936f51e6f77 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 40.537ms
2025-08-19 11:50:22.242 | INFO     | be85c8d9ceb74ab7bffdd1952861c39c | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:22.244 | INFO     | be85c8d9ceb74ab7bffdd1952861c39c | 成功认证Java用户: pythontest
2025-08-19 11:50:22.262 | INFO     | be85c8d9ceb74ab7bffdd1952861c39c | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 11:50:22.265 | INFO     | be85c8d9ceb74ab7bffdd1952861c39c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 23.275ms
2025-08-19 11:50:22.268 | INFO     | f834e1c88a0944938975f306f5e12284 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:22.269 | INFO     | f834e1c88a0944938975f306f5e12284 | 成功认证Java用户: pythontest
2025-08-19 11:50:22.276 | INFO     | f834e1c88a0944938975f306f5e12284 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:22.277 | INFO     | f834e1c88a0944938975f306f5e12284 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:50:22.296 | INFO     | f834e1c88a0944938975f306f5e12284 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:22.298 | INFO     | f834e1c88a0944938975f306f5e12284 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 30.998ms
2025-08-19 11:50:22.300 | INFO     | 3564ba990e2740ccb8546674267b3ba4 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:22.302 | INFO     | 3564ba990e2740ccb8546674267b3ba4 | 成功认证Java用户: pythontest
2025-08-19 11:50:22.307 | INFO     | 3564ba990e2740ccb8546674267b3ba4 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:22.308 | INFO     | 3564ba990e2740ccb8546674267b3ba4 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:50:22.328 | INFO     | 3564ba990e2740ccb8546674267b3ba4 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:22.330 | INFO     | 3564ba990e2740ccb8546674267b3ba4 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 29.606ms
2025-08-19 11:50:24.193 | INFO     | 217899791b244ffab05f4f4854146a7e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:24.195 | INFO     | 217899791b244ffab05f4f4854146a7e | 成功认证Java用户: pythontest
2025-08-19 11:50:24.203 | INFO     | 217899791b244ffab05f4f4854146a7e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:24.204 | INFO     | 217899791b244ffab05f4f4854146a7e | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-19 11:50:24.362 | INFO     | 217899791b244ffab05f4f4854146a7e | HTTP Request: DELETE http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:50:24.364 | INFO     | 217899791b244ffab05f4f4854146a7e | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 171.203ms
2025-08-19 11:50:24.372 | INFO     | 959349d037bf4c63aaa4d37f52e959e9 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:24.374 | INFO     | 8e65113621e249af967412ddd7265825 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:24.375 | INFO     | 959349d037bf4c63aaa4d37f52e959e9 | 成功认证Java用户: pythontest
2025-08-19 11:50:24.377 | INFO     | 8e65113621e249af967412ddd7265825 | 成功认证Java用户: pythontest
2025-08-19 11:50:24.384 | INFO     | 959349d037bf4c63aaa4d37f52e959e9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:24.386 | INFO     | 959349d037bf4c63aaa4d37f52e959e9 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:50:24.390 | INFO     | 8e65113621e249af967412ddd7265825 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:24.392 | INFO     | 8e65113621e249af967412ddd7265825 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:50:24.416 | INFO     | 959349d037bf4c63aaa4d37f52e959e9 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:24.418 | INFO     | 8e65113621e249af967412ddd7265825 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:24.419 | INFO     | 959349d037bf4c63aaa4d37f52e959e9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 47.078ms
2025-08-19 11:50:24.421 | INFO     | 8e65113621e249af967412ddd7265825 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 47.181ms
2025-08-19 11:50:26.009 | INFO     | 56351bbd564c4c599ecee446c18b2795 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:26.011 | INFO     | 56351bbd564c4c599ecee446c18b2795 | 成功认证Java用户: pythontest
2025-08-19 11:50:26.018 | INFO     | 56351bbd564c4c599ecee446c18b2795 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:26.019 | INFO     | 56351bbd564c4c599ecee446c18b2795 | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-19 11:50:26.203 | INFO     | 56351bbd564c4c599ecee446c18b2795 | HTTP Request: DELETE http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:50:26.205 | INFO     | 56351bbd564c4c599ecee446c18b2795 | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 196.117ms
2025-08-19 11:50:26.219 | INFO     | b7f043efb3ce4b6783410099c98adeca | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:26.220 | INFO     | 537f4d6d05a747e78ae4f2696224c307 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:26.224 | INFO     | b7f043efb3ce4b6783410099c98adeca | 成功认证Java用户: pythontest
2025-08-19 11:50:26.224 | INFO     | 537f4d6d05a747e78ae4f2696224c307 | 成功认证Java用户: pythontest
2025-08-19 11:50:26.233 | INFO     | b7f043efb3ce4b6783410099c98adeca | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:26.234 | INFO     | b7f043efb3ce4b6783410099c98adeca | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:50:26.239 | INFO     | 537f4d6d05a747e78ae4f2696224c307 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:26.240 | INFO     | 537f4d6d05a747e78ae4f2696224c307 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:50:26.268 | INFO     | 537f4d6d05a747e78ae4f2696224c307 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:26.269 | INFO     | b7f043efb3ce4b6783410099c98adeca | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:26.272 | INFO     | 537f4d6d05a747e78ae4f2696224c307 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 52.264ms
2025-08-19 11:50:26.273 | INFO     | b7f043efb3ce4b6783410099c98adeca | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 54.671ms
2025-08-19 11:50:27.558 | INFO     | 615d6bd105124cacbaaf8b51d5382adc | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:27.559 | INFO     | 615d6bd105124cacbaaf8b51d5382adc | 成功认证Java用户: pythontest
2025-08-19 11:50:27.567 | INFO     | 615d6bd105124cacbaaf8b51d5382adc | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:27.569 | INFO     | 615d6bd105124cacbaaf8b51d5382adc | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-19 11:50:27.749 | INFO     | 615d6bd105124cacbaaf8b51d5382adc | HTTP Request: DELETE http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:50:27.751 | INFO     | 615d6bd105124cacbaaf8b51d5382adc | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 194.380ms
2025-08-19 11:50:27.759 | INFO     | 8f6863eff94a4280b442df207298ed01 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:27.762 | INFO     | a28f987e4f1f46baad04286697ec5ee5 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:27.763 | INFO     | 8f6863eff94a4280b442df207298ed01 | 成功认证Java用户: pythontest
2025-08-19 11:50:27.764 | INFO     | a28f987e4f1f46baad04286697ec5ee5 | 成功认证Java用户: pythontest
2025-08-19 11:50:27.773 | INFO     | a28f987e4f1f46baad04286697ec5ee5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:27.774 | INFO     | a28f987e4f1f46baad04286697ec5ee5 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:50:27.779 | INFO     | 8f6863eff94a4280b442df207298ed01 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:27.779 | INFO     | 8f6863eff94a4280b442df207298ed01 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:50:27.804 | INFO     | a28f987e4f1f46baad04286697ec5ee5 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:27.805 | INFO     | 8f6863eff94a4280b442df207298ed01 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:27.808 | INFO     | a28f987e4f1f46baad04286697ec5ee5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 47.618ms
2025-08-19 11:50:27.810 | INFO     | 8f6863eff94a4280b442df207298ed01 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 51.046ms
2025-08-19 11:50:28.927 | INFO     | 02eff211a95847cb836f34d132ace6f7 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:28.929 | INFO     | 02eff211a95847cb836f34d132ace6f7 | 成功认证Java用户: pythontest
2025-08-19 11:50:28.937 | INFO     | 02eff211a95847cb836f34d132ace6f7 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:28.938 | INFO     | 02eff211a95847cb836f34d132ace6f7 | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-19 11:50:29.094 | INFO     | 02eff211a95847cb836f34d132ace6f7 | HTTP Request: DELETE http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:50:29.096 | INFO     | 02eff211a95847cb836f34d132ace6f7 | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 169.316ms
2025-08-19 11:50:29.103 | INFO     | 7b5efa076f994fe185993a8f3ee0af3e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:29.105 | INFO     | 6b1d36515f034388a91698f15ef348ef | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:29.105 | INFO     | 7b5efa076f994fe185993a8f3ee0af3e | 成功认证Java用户: pythontest
2025-08-19 11:50:29.106 | INFO     | 6b1d36515f034388a91698f15ef348ef | 成功认证Java用户: pythontest
2025-08-19 11:50:29.116 | INFO     | 6b1d36515f034388a91698f15ef348ef | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:29.116 | INFO     | 6b1d36515f034388a91698f15ef348ef | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:50:29.125 | INFO     | 7b5efa076f994fe185993a8f3ee0af3e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:29.127 | INFO     | 7b5efa076f994fe185993a8f3ee0af3e | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:50:29.142 | INFO     | 6b1d36515f034388a91698f15ef348ef | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:29.144 | INFO     | 6b1d36515f034388a91698f15ef348ef | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 39.998ms
2025-08-19 11:50:29.151 | INFO     | 7b5efa076f994fe185993a8f3ee0af3e | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:29.153 | INFO     | 7b5efa076f994fe185993a8f3ee0af3e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 50.339ms
2025-08-19 11:50:30.718 | INFO     | 8b8a87c3d04841409df32af01e0cdbae | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:30.720 | INFO     | 8b8a87c3d04841409df32af01e0cdbae | 成功认证Java用户: pythontest
2025-08-19 11:50:30.729 | INFO     | 8b8a87c3d04841409df32af01e0cdbae | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:30.731 | INFO     | 8b8a87c3d04841409df32af01e0cdbae | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-19 11:50:30.862 | INFO     | 8b8a87c3d04841409df32af01e0cdbae | HTTP Request: DELETE http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:50:30.864 | INFO     | 8b8a87c3d04841409df32af01e0cdbae | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 146.456ms
2025-08-19 11:50:30.872 | INFO     | c715f4017b064540b2dd9d605b390a85 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:30.874 | INFO     | cd94134640b242ac9482419a2fd883af | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:30.876 | INFO     | c715f4017b064540b2dd9d605b390a85 | 成功认证Java用户: pythontest
2025-08-19 11:50:30.877 | INFO     | cd94134640b242ac9482419a2fd883af | 成功认证Java用户: pythontest
2025-08-19 11:50:30.885 | INFO     | cd94134640b242ac9482419a2fd883af | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:30.886 | INFO     | cd94134640b242ac9482419a2fd883af | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:50:30.891 | INFO     | c715f4017b064540b2dd9d605b390a85 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:30.892 | INFO     | c715f4017b064540b2dd9d605b390a85 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:50:30.919 | INFO     | cd94134640b242ac9482419a2fd883af | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:30.922 | INFO     | c715f4017b064540b2dd9d605b390a85 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:30.924 | INFO     | cd94134640b242ac9482419a2fd883af | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 50.402ms
2025-08-19 11:50:30.927 | INFO     | c715f4017b064540b2dd9d605b390a85 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 54.963ms
2025-08-19 11:50:33.667 | INFO     | e1f11d42020d497380b466be9433892b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:33.670 | INFO     | e1f11d42020d497380b466be9433892b | 成功认证Java用户: pythontest
2025-08-19 11:50:33.680 | INFO     | e1f11d42020d497380b466be9433892b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:33.681 | INFO     | e1f11d42020d497380b466be9433892b | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-19 11:50:33.836 | INFO     | e1f11d42020d497380b466be9433892b | HTTP Request: DELETE http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:50:33.837 | INFO     | e1f11d42020d497380b466be9433892b | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 170.222ms
2025-08-19 11:50:33.845 | INFO     | f7812fc1ae5c498dbb9b98ea95c668b7 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:33.845 | INFO     | 161301faad4b46ecb6fe48fec24b32c2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:33.847 | INFO     | f7812fc1ae5c498dbb9b98ea95c668b7 | 成功认证Java用户: pythontest
2025-08-19 11:50:33.849 | INFO     | 161301faad4b46ecb6fe48fec24b32c2 | 成功认证Java用户: pythontest
2025-08-19 11:50:33.856 | INFO     | 161301faad4b46ecb6fe48fec24b32c2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:33.857 | INFO     | 161301faad4b46ecb6fe48fec24b32c2 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:50:33.863 | INFO     | f7812fc1ae5c498dbb9b98ea95c668b7 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:33.863 | INFO     | f7812fc1ae5c498dbb9b98ea95c668b7 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:50:33.889 | INFO     | 161301faad4b46ecb6fe48fec24b32c2 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:33.891 | INFO     | f7812fc1ae5c498dbb9b98ea95c668b7 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:33.892 | INFO     | 161301faad4b46ecb6fe48fec24b32c2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 46.918ms
2025-08-19 11:50:33.895 | INFO     | f7812fc1ae5c498dbb9b98ea95c668b7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 51.112ms
2025-08-19 11:52:48.783 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-19 11:55:03.278 | INFO     | c50633c8f69f426ba779318d1999aca5 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 11:55:03.279 | INFO     | c50633c8f69f426ba779318d1999aca5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 23.614ms
2025-08-19 11:55:03.674 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:55:03.676 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | 成功认证Java用户: pythontest
2025-08-19 11:55:03.689 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:55:03.689 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 11:55:03.689 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | 创建知识库请求数据: {'name': 'test_updated_api_536955', 'description': '测试重启后的接口', 'permission': 'me', 'chunk_method': 'naive', 'embedding_model': 'bge-m3:latest@Ollama'}
2025-08-19 11:55:03.703 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:55:03.704 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | 尝试使用备用模型: BAAI/bge-zh-v1.5
2025-08-19 11:55:03.713 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:55:03.714 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | 尝试使用备用模型: BAAI/bge-large-zh-v1.5
2025-08-19 11:55:03.722 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:55:03.723 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | 尝试使用备用模型: text-embedding-ada-002
2025-08-19 11:55:03.732 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:55:03.733 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 62.769ms
2025-08-19 11:55:47.003 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-19 11:56:16.227 | INFO     | 4c7a61056b8f40f18fd4aa9299c1af33 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 11:56:16.229 | INFO     | 4c7a61056b8f40f18fd4aa9299c1af33 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 22.588ms
2025-08-19 11:56:16.619 | INFO     | 8a3741e075e1418b8765e63dac83965b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:56:16.620 | INFO     | 8a3741e075e1418b8765e63dac83965b | 成功认证Java用户: pythontest
2025-08-19 11:56:16.634 | INFO     | 8a3741e075e1418b8765e63dac83965b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:56:16.634 | INFO     | 8a3741e075e1418b8765e63dac83965b | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 11:56:16.634 | INFO     | 8a3741e075e1418b8765e63dac83965b | 创建知识库请求数据: {'name': 'test_updated_api_537028', 'description': '测试重启后的接口', 'permission': 'me', 'chunk_method': 'naive'}
2025-08-19 11:56:16.955 | INFO     | 8a3741e075e1418b8765e63dac83965b | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:56:16.956 | INFO     | 8a3741e075e1418b8765e63dac83965b | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 340.656ms
2025-08-19 11:57:19.477 | INFO     | 2fe60a141a404ac4ae1fc662f6a1c0a5 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:57:19.480 | INFO     | 2fe60a141a404ac4ae1fc662f6a1c0a5 | 成功认证Java用户: pythontest
2025-08-19 11:57:19.499 | INFO     | 2fe60a141a404ac4ae1fc662f6a1c0a5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:57:19.500 | INFO     | 2fe60a141a404ac4ae1fc662f6a1c0a5 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:57:19.516 | INFO     | 2fe60a141a404ac4ae1fc662f6a1c0a5 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:57:19.517 | INFO     | 2fe60a141a404ac4ae1fc662f6a1c0a5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 40.129ms
2025-08-19 11:57:25.617 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:57:25.619 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | 成功认证Java用户: pythontest
2025-08-19 11:57:25.630 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:57:25.631 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 11:57:25.632 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | 创建知识库请求数据: {'name': 'pythontest2', 'description': '', 'embedding_model': 'bge-m3:latest@Ollama', 'permission': 'me', 'chunk_method': 'naive', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'auto_keywords': 0, 'auto_questions': 0, 'task_page_size': 12}}
2025-08-19 11:57:25.787 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:57:25.789 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | 尝试使用备用模型: BAAI/bge-zh-v1.5
2025-08-19 11:57:25.800 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:57:25.801 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | 尝试使用备用模型: BAAI/bge-large-zh-v1.5
2025-08-19 11:57:25.811 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:57:25.813 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | 尝试使用备用模型: text-embedding-ada-002
2025-08-19 11:57:25.823 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:57:25.825 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 208.528ms
2025-08-19 11:58:33.759 | INFO     | a6fad0f8a97a4fdba16b523e6492a73e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:58:33.760 | INFO     | 8e950f2cc27b4b4eacfa902f9a119ccf | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:58:33.761 | INFO     | 0d378a34e83842f081cfe9134b3cc6f6 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:58:33.762 | INFO     | a6fad0f8a97a4fdba16b523e6492a73e | 成功认证Java用户: pythontest
2025-08-19 11:58:33.764 | INFO     | 8e950f2cc27b4b4eacfa902f9a119ccf | 成功认证Java用户: pythontest
2025-08-19 11:58:33.765 | INFO     | 0d378a34e83842f081cfe9134b3cc6f6 | 成功认证Java用户: pythontest
2025-08-19 11:58:33.773 | INFO     | 0d378a34e83842f081cfe9134b3cc6f6 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:58:33.773 | INFO     | 0d378a34e83842f081cfe9134b3cc6f6 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:58:33.778 | INFO     | 8e950f2cc27b4b4eacfa902f9a119ccf | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:58:33.779 | INFO     | 8e950f2cc27b4b4eacfa902f9a119ccf | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:58:33.783 | INFO     | a6fad0f8a97a4fdba16b523e6492a73e | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 11:58:33.784 | INFO     | a6fad0f8a97a4fdba16b523e6492a73e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 24.962ms
2025-08-19 11:58:33.788 | INFO     | 0d378a34e83842f081cfe9134b3cc6f6 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:58:33.789 | INFO     | 0d378a34e83842f081cfe9134b3cc6f6 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 29.405ms
2025-08-19 11:58:33.793 | INFO     | 8e950f2cc27b4b4eacfa902f9a119ccf | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:58:33.794 | INFO     | 8e950f2cc27b4b4eacfa902f9a119ccf | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 33.430ms
2025-08-19 11:58:54.025 | INFO     | 898fa2e4f2b34685841f7ed95cc4ca07 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:58:54.027 | INFO     | 898fa2e4f2b34685841f7ed95cc4ca07 | 成功认证Java用户: pythontest
2025-08-19 11:58:54.040 | INFO     | 898fa2e4f2b34685841f7ed95cc4ca07 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 11:58:54.041 | INFO     | 898fa2e4f2b34685841f7ed95cc4ca07 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 15.791ms
2025-08-19 11:58:54.043 | INFO     | 6fef77cf952e4fa485936735632d2547 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:58:54.044 | INFO     | 6fef77cf952e4fa485936735632d2547 | 成功认证Java用户: pythontest
2025-08-19 11:58:54.049 | INFO     | 6fef77cf952e4fa485936735632d2547 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:58:54.050 | INFO     | 6fef77cf952e4fa485936735632d2547 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:58:54.063 | INFO     | 6fef77cf952e4fa485936735632d2547 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:58:54.064 | INFO     | 6fef77cf952e4fa485936735632d2547 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 21.185ms
2025-08-19 11:58:54.066 | INFO     | 0f70ce47078042b9b83f71bd66dbdd37 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:58:54.068 | INFO     | 0f70ce47078042b9b83f71bd66dbdd37 | 成功认证Java用户: pythontest
2025-08-19 11:58:54.073 | INFO     | 0f70ce47078042b9b83f71bd66dbdd37 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:58:54.073 | INFO     | 0f70ce47078042b9b83f71bd66dbdd37 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:58:54.087 | INFO     | 0f70ce47078042b9b83f71bd66dbdd37 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:58:54.088 | INFO     | 0f70ce47078042b9b83f71bd66dbdd37 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 21.319ms
2025-08-19 13:31:43.685 | INFO     | aa0d8ae6481c4cd89e1256217f768a36 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:31:43.751 | INFO     | aa0d8ae6481c4cd89e1256217f768a36 | 成功认证Java用户: pythontest
2025-08-19 13:31:44.117 | INFO     | aa0d8ae6481c4cd89e1256217f768a36 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:31:44.118 | INFO     | aa0d8ae6481c4cd89e1256217f768a36 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 13:31:44.118 | INFO     | aa0d8ae6481c4cd89e1256217f768a36 | 创建知识库请求数据: {'name': 'frontend_fixed_test_542755', 'description': '测试前端修复后的接口', 'permission': 'me', 'chunk_method': 'naive', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'auto_keywords': 0, 'auto_questions': 0, 'task_page_size': 12}}
2025-08-19 13:31:44.551 | INFO     | aa0d8ae6481c4cd89e1256217f768a36 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:31:44.552 | INFO     | aa0d8ae6481c4cd89e1256217f768a36 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 867.315ms
2025-08-19 13:31:44.950 | INFO     | ******************************** | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:31:44.950 | INFO     | ******************************** | 成功认证Java用户: pythontest
2025-08-19 13:31:44.967 | INFO     | ******************************** | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:31:44.967 | INFO     | ******************************** | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 13:31:44.968 | INFO     | ******************************** | 创建知识库请求数据: {'name': 'explicit_model_test_542756', 'description': '测试明确指定嵌入模型', 'embedding_model': 'text-embedding-bge-m3@LM-Studio', 'permission': 'me', 'chunk_method': 'naive'}
2025-08-19 13:31:44.984 | INFO     | ******************************** | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:31:44.985 | INFO     | ******************************** | 尝试使用备用模型: BAAI/bge-zh-v1.5
2025-08-19 13:31:44.996 | INFO     | ******************************** | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:31:44.997 | INFO     | ******************************** | 尝试使用备用模型: BAAI/bge-large-zh-v1.5
2025-08-19 13:31:45.008 | INFO     | ******************************** | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:31:45.009 | INFO     | ******************************** | 尝试使用备用模型: text-embedding-ada-002
2025-08-19 13:31:45.021 | INFO     | ******************************** | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:31:45.022 | INFO     | ******************************** | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 72.546ms
2025-08-19 13:32:58.649 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:32:58.660 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | 成功认证Java用户: pythontest
2025-08-19 13:32:58.675 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:32:58.676 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 13:32:58.676 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | 创建知识库请求数据: {'name': 'pythontest2', 'description': '', 'embedding_model': 'bge-m3:latest@Ollama', 'permission': 'me', 'chunk_method': 'naive', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'auto_keywords': 0, 'auto_questions': 0, 'task_page_size': 12}}
2025-08-19 13:32:58.692 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:32:58.693 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | 尝试使用备用模型: BAAI/bge-zh-v1.5
2025-08-19 13:32:58.703 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:32:58.704 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | 尝试使用备用模型: BAAI/bge-large-zh-v1.5
2025-08-19 13:32:58.714 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:32:58.715 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | 尝试使用备用模型: text-embedding-ada-002
2025-08-19 13:32:58.726 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:32:58.727 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 77.288ms
2025-08-19 13:33:15.600 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-19 13:33:20.927 | INFO     | dfc902a2fa474926a595eed538191643 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:33:20.929 | INFO     | 464b59908a874129aac539e211366bfd | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:33:20.932 | INFO     | dfc902a2fa474926a595eed538191643 | 成功认证Java用户: pythontest
2025-08-19 13:33:20.935 | INFO     | 464b59908a874129aac539e211366bfd | 成功认证Java用户: pythontest
2025-08-19 13:33:20.972 | INFO     | dfc902a2fa474926a595eed538191643 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:33:20.975 | INFO     | dfc902a2fa474926a595eed538191643 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 56.587ms
2025-08-19 13:33:20.977 | INFO     | 464b59908a874129aac539e211366bfd | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:33:20.977 | INFO     | 464b59908a874129aac539e211366bfd | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:33:20.982 | INFO     | d3391c225dd545908bce5c5ed7ceaec2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:33:20.985 | INFO     | d3391c225dd545908bce5c5ed7ceaec2 | 成功认证Java用户: pythontest
2025-08-19 13:33:21.004 | INFO     | 464b59908a874129aac539e211366bfd | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:33:21.006 | INFO     | 464b59908a874129aac539e211366bfd | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 76.729ms
2025-08-19 13:33:21.011 | INFO     | d3391c225dd545908bce5c5ed7ceaec2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:33:21.011 | INFO     | d3391c225dd545908bce5c5ed7ceaec2 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:33:21.027 | INFO     | d3391c225dd545908bce5c5ed7ceaec2 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:33:21.029 | INFO     | d3391c225dd545908bce5c5ed7ceaec2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 47.833ms
2025-08-19 13:33:22.440 | INFO     | d991147887924ef5bc2c48f652fe6a86 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:33:22.441 | INFO     | fdd872a105e24437beb9d6722af4cdf4 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:33:22.442 | INFO     | d991147887924ef5bc2c48f652fe6a86 | 成功认证Java用户: pythontest
2025-08-19 13:33:22.443 | INFO     | 1e2a90f1cc9748059fe3ec981a6c64fd | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:33:22.444 | INFO     | fdd872a105e24437beb9d6722af4cdf4 | 成功认证Java用户: pythontest
2025-08-19 13:33:22.448 | INFO     | 1e2a90f1cc9748059fe3ec981a6c64fd | 成功认证Java用户: pythontest
2025-08-19 13:33:22.463 | INFO     | 1e2a90f1cc9748059fe3ec981a6c64fd | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:33:22.464 | INFO     | 1e2a90f1cc9748059fe3ec981a6c64fd | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:33:22.471 | INFO     | d991147887924ef5bc2c48f652fe6a86 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:33:22.478 | INFO     | d991147887924ef5bc2c48f652fe6a86 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 38.374ms
2025-08-19 13:33:22.486 | INFO     | fdd872a105e24437beb9d6722af4cdf4 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:33:22.487 | INFO     | fdd872a105e24437beb9d6722af4cdf4 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:33:22.492 | INFO     | 1e2a90f1cc9748059fe3ec981a6c64fd | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:33:22.498 | INFO     | 1e2a90f1cc9748059fe3ec981a6c64fd | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 55.987ms
2025-08-19 13:33:22.511 | INFO     | fdd872a105e24437beb9d6722af4cdf4 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:33:22.513 | INFO     | fdd872a105e24437beb9d6722af4cdf4 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 72.445ms
2025-08-19 13:33:34.259 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:33:34.260 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | 成功认证Java用户: pythontest
2025-08-19 13:33:34.273 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:33:34.273 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 13:33:34.274 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | 创建知识库请求数据: {'name': 'pythontest2', 'description': '', 'embedding_model': 'bge-m3:latest@Ollama', 'permission': 'me', 'chunk_method': 'naive', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'auto_keywords': 0, 'auto_questions': 0, 'task_page_size': 12}}
2025-08-19 13:33:34.291 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:33:34.293 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | 尝试使用备用模型: BAAI/bge-zh-v1.5
2025-08-19 13:33:34.303 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:33:34.305 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | 尝试使用备用模型: BAAI/bge-large-zh-v1.5
2025-08-19 13:33:34.319 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:33:34.320 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | 尝试使用备用模型: text-embedding-ada-002
2025-08-19 13:33:34.333 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:33:34.334 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 75.933ms
2025-08-19 13:34:23.224 | INFO     | 5706e527c70d4fc788277cb0fcf054e8 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:34:23.226 | INFO     | 771c66f6d19549e5ae85f45bcc50a3ad | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:34:23.226 | INFO     | cc50d91095254f23abddb85f767ad6e1 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:34:23.228 | INFO     | 5706e527c70d4fc788277cb0fcf054e8 | 成功认证Java用户: pythontest
2025-08-19 13:34:23.230 | INFO     | 771c66f6d19549e5ae85f45bcc50a3ad | 成功认证Java用户: pythontest
2025-08-19 13:34:23.232 | INFO     | cc50d91095254f23abddb85f767ad6e1 | 成功认证Java用户: pythontest
2025-08-19 13:34:23.244 | INFO     | cc50d91095254f23abddb85f767ad6e1 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:34:23.245 | INFO     | cc50d91095254f23abddb85f767ad6e1 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:34:23.251 | INFO     | 771c66f6d19549e5ae85f45bcc50a3ad | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:34:23.252 | INFO     | 771c66f6d19549e5ae85f45bcc50a3ad | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:34:23.257 | INFO     | 5706e527c70d4fc788277cb0fcf054e8 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:34:23.263 | INFO     | 5706e527c70d4fc788277cb0fcf054e8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 39.659ms
2025-08-19 13:34:23.279 | INFO     | cc50d91095254f23abddb85f767ad6e1 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:34:23.281 | INFO     | 771c66f6d19549e5ae85f45bcc50a3ad | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:34:23.282 | INFO     | cc50d91095254f23abddb85f767ad6e1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 57.603ms
2025-08-19 13:34:23.285 | INFO     | 771c66f6d19549e5ae85f45bcc50a3ad | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 60.083ms
2025-08-19 13:34:50.674 | INFO     | 80d44c8096d7422fa80537ea156d5326 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:34:50.675 | INFO     | 80d44c8096d7422fa80537ea156d5326 | 成功认证Java用户: pythontest
2025-08-19 13:34:50.683 | INFO     | 80d44c8096d7422fa80537ea156d5326 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:34:50.685 | INFO     | 80d44c8096d7422fa80537ea156d5326 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 13:34:50.685 | INFO     | 80d44c8096d7422fa80537ea156d5326 | 创建知识库请求数据: {'name': 'pythontest2', 'description': '', 'permission': 'me', 'chunk_method': 'naive', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'auto_keywords': 0, 'auto_questions': 0, 'task_page_size': 12}}
2025-08-19 13:34:50.873 | INFO     | 80d44c8096d7422fa80537ea156d5326 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:34:50.874 | INFO     | 80d44c8096d7422fa80537ea156d5326 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 200.936ms
2025-08-19 13:34:50.888 | INFO     | fb0ff833b7554c3498aa31028f112d77 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:34:50.891 | INFO     | fb0ff833b7554c3498aa31028f112d77 | 成功认证Java用户: pythontest
2025-08-19 13:34:50.900 | INFO     | fb0ff833b7554c3498aa31028f112d77 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:34:50.901 | INFO     | fb0ff833b7554c3498aa31028f112d77 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:34:50.917 | INFO     | fb0ff833b7554c3498aa31028f112d77 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:34:50.919 | INFO     | fb0ff833b7554c3498aa31028f112d77 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 30.754ms
2025-08-19 13:34:50.923 | INFO     | 4f7ab12c8d9b4d1e97b5f672e517a755 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:34:50.924 | INFO     | 4f7ab12c8d9b4d1e97b5f672e517a755 | 成功认证Java用户: pythontest
2025-08-19 13:34:50.932 | INFO     | 4f7ab12c8d9b4d1e97b5f672e517a755 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:34:50.933 | INFO     | 4f7ab12c8d9b4d1e97b5f672e517a755 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:34:50.951 | INFO     | 4f7ab12c8d9b4d1e97b5f672e517a755 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:34:50.953 | INFO     | 4f7ab12c8d9b4d1e97b5f672e517a755 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 31.037ms
2025-08-19 13:34:53.279 | INFO     | 75a9b9d29cd44d14ba466f6ecd534d00 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:34:53.281 | INFO     | 75a9b9d29cd44d14ba466f6ecd534d00 | 成功认证Java用户: pythontest
2025-08-19 13:34:53.288 | INFO     | 75a9b9d29cd44d14ba466f6ecd534d00 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:34:53.288 | INFO     | 75a9b9d29cd44d14ba466f6ecd534d00 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 13:34:53.386 | INFO     | 75a9b9d29cd44d14ba466f6ecd534d00 | HTTP Request: GET http://*************:6610/api/v1/datasets/3a54e7487cbe11f0a8f6ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 13:34:53.388 | INFO     | 75a9b9d29cd44d14ba466f6ecd534d00 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/3a54e7487cbe11f0a8f6ea5dc8d5776c | 108.836ms
2025-08-19 13:34:54.357 | INFO     | 88247eabc6f246268fcec1dc25b5ecfc | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:34:54.359 | INFO     | 88247eabc6f246268fcec1dc25b5ecfc | 成功认证Java用户: pythontest
2025-08-19 13:34:54.366 | INFO     | 88247eabc6f246268fcec1dc25b5ecfc | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:34:54.367 | INFO     | 88247eabc6f246268fcec1dc25b5ecfc | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 13:34:54.378 | INFO     | 88247eabc6f246268fcec1dc25b5ecfc | HTTP Request: GET http://*************:6610/api/v1/datasets/3a54e7487cbe11f0a8f6ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 13:34:54.380 | INFO     | 88247eabc6f246268fcec1dc25b5ecfc | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/3a54e7487cbe11f0a8f6ea5dc8d5776c | 23.252ms
2025-08-19 13:34:54.826 | INFO     | afc1fe9a679c4f5b95073a807481c0cf | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:34:54.829 | INFO     | afc1fe9a679c4f5b95073a807481c0cf | 成功认证Java用户: pythontest
2025-08-19 13:34:54.836 | INFO     | afc1fe9a679c4f5b95073a807481c0cf | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:34:54.836 | INFO     | afc1fe9a679c4f5b95073a807481c0cf | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 13:34:54.848 | INFO     | afc1fe9a679c4f5b95073a807481c0cf | HTTP Request: GET http://*************:6610/api/v1/datasets/3a54e7487cbe11f0a8f6ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 13:34:54.850 | INFO     | afc1fe9a679c4f5b95073a807481c0cf | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/3a54e7487cbe11f0a8f6ea5dc8d5776c | 23.707ms
2025-08-19 13:34:55.105 | INFO     | bb23a05a79e44477a8892be90c5ed3b9 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:34:55.106 | INFO     | bb23a05a79e44477a8892be90c5ed3b9 | 成功认证Java用户: pythontest
2025-08-19 13:34:55.114 | INFO     | bb23a05a79e44477a8892be90c5ed3b9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:34:55.115 | INFO     | bb23a05a79e44477a8892be90c5ed3b9 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 13:34:55.128 | INFO     | bb23a05a79e44477a8892be90c5ed3b9 | HTTP Request: GET http://*************:6610/api/v1/datasets/3a54e7487cbe11f0a8f6ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 13:34:55.131 | INFO     | bb23a05a79e44477a8892be90c5ed3b9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/3a54e7487cbe11f0a8f6ea5dc8d5776c | 26.744ms
2025-08-19 13:34:55.477 | INFO     | 6da2178367094455824c7fa9aeb4958d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:34:55.479 | INFO     | 6da2178367094455824c7fa9aeb4958d | 成功认证Java用户: pythontest
2025-08-19 13:34:55.485 | INFO     | 6da2178367094455824c7fa9aeb4958d | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:34:55.485 | INFO     | 6da2178367094455824c7fa9aeb4958d | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 13:34:55.498 | INFO     | 6da2178367094455824c7fa9aeb4958d | HTTP Request: GET http://*************:6610/api/v1/datasets/3a54e7487cbe11f0a8f6ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 13:34:55.500 | INFO     | 6da2178367094455824c7fa9aeb4958d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/3a54e7487cbe11f0a8f6ea5dc8d5776c | 22.536ms
2025-08-19 13:35:08.406 | INFO     | ff8917aaa72b4c2c84f3ba35a2eb18ea | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:35:08.407 | INFO     | ff8917aaa72b4c2c84f3ba35a2eb18ea | 成功认证Java用户: pythontest
2025-08-19 13:35:08.416 | INFO     | ff8917aaa72b4c2c84f3ba35a2eb18ea | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:35:08.418 | INFO     | ff8917aaa72b4c2c84f3ba35a2eb18ea | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-19 13:35:08.563 | INFO     | ff8917aaa72b4c2c84f3ba35a2eb18ea | HTTP Request: DELETE http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:35:08.564 | INFO     | ff8917aaa72b4c2c84f3ba35a2eb18ea | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 157.620ms
2025-08-19 13:35:08.575 | INFO     | 97f839e3e1604c1abb8d3cd2a019aa8f | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:35:08.578 | INFO     | 97f839e3e1604c1abb8d3cd2a019aa8f | 成功认证Java用户: pythontest
2025-08-19 13:35:08.586 | INFO     | 97f839e3e1604c1abb8d3cd2a019aa8f | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:35:08.588 | INFO     | 97f839e3e1604c1abb8d3cd2a019aa8f | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:35:08.603 | INFO     | 97f839e3e1604c1abb8d3cd2a019aa8f | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:35:08.605 | INFO     | 97f839e3e1604c1abb8d3cd2a019aa8f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 30.723ms
2025-08-19 13:35:08.609 | INFO     | 39a118930eb14351954936360ebba3e4 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:35:08.610 | INFO     | 39a118930eb14351954936360ebba3e4 | 成功认证Java用户: pythontest
2025-08-19 13:35:08.617 | INFO     | 39a118930eb14351954936360ebba3e4 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:35:08.618 | INFO     | 39a118930eb14351954936360ebba3e4 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:35:08.637 | INFO     | 39a118930eb14351954936360ebba3e4 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:35:08.639 | INFO     | 39a118930eb14351954936360ebba3e4 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 30.618ms
2025-08-19 13:35:10.786 | INFO     | 8b3438fb1dfb47f885c99d1ef971ac2e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:35:10.787 | INFO     | 8b3438fb1dfb47f885c99d1ef971ac2e | 成功认证Java用户: pythontest
2025-08-19 13:35:10.795 | INFO     | 8b3438fb1dfb47f885c99d1ef971ac2e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:35:10.796 | INFO     | 8b3438fb1dfb47f885c99d1ef971ac2e | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-19 13:35:11.121 | INFO     | 8b3438fb1dfb47f885c99d1ef971ac2e | HTTP Request: DELETE http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:35:11.123 | INFO     | 8b3438fb1dfb47f885c99d1ef971ac2e | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 337.540ms
2025-08-19 13:35:11.135 | INFO     | e33f36231adb4b8897a21d159a792440 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:35:11.137 | INFO     | 0e1a089ca6254fee81de5eff254619af | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:35:11.138 | INFO     | e33f36231adb4b8897a21d159a792440 | 成功认证Java用户: pythontest
2025-08-19 13:35:11.141 | INFO     | 0e1a089ca6254fee81de5eff254619af | 成功认证Java用户: pythontest
2025-08-19 13:35:11.149 | INFO     | e33f36231adb4b8897a21d159a792440 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:35:11.150 | INFO     | e33f36231adb4b8897a21d159a792440 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:35:11.155 | INFO     | 0e1a089ca6254fee81de5eff254619af | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:35:11.156 | INFO     | 0e1a089ca6254fee81de5eff254619af | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:35:11.200 | INFO     | 0e1a089ca6254fee81de5eff254619af | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:35:11.201 | INFO     | e33f36231adb4b8897a21d159a792440 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:35:11.204 | INFO     | 0e1a089ca6254fee81de5eff254619af | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 67.908ms
2025-08-19 13:35:11.205 | INFO     | e33f36231adb4b8897a21d159a792440 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 70.552ms
2025-08-19 13:35:15.805 | INFO     | 91664892a78c4344ba421d3885e35cc6 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:35:15.806 | INFO     | 91664892a78c4344ba421d3885e35cc6 | 成功认证Java用户: pythontest
2025-08-19 13:35:15.813 | INFO     | 91664892a78c4344ba421d3885e35cc6 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:35:15.814 | INFO     | 91664892a78c4344ba421d3885e35cc6 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 13:35:15.825 | INFO     | 91664892a78c4344ba421d3885e35cc6 | HTTP Request: GET http://*************:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16 "HTTP/1.1 200 OK"
2025-08-19 13:35:15.827 | INFO     | 91664892a78c4344ba421d3885e35cc6 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/87a3346677ee11f0a8f37e63526e5b16 | 22.972ms
2025-08-19 13:35:20.687 | INFO     | 6fa401bf314e4f80bf74a784bef857f0 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:35:20.689 | INFO     | 6fa401bf314e4f80bf74a784bef857f0 | 成功认证Java用户: pythontest
2025-08-19 13:35:20.694 | INFO     | 6fa401bf314e4f80bf74a784bef857f0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:35:20.694 | INFO     | 6fa401bf314e4f80bf74a784bef857f0 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 13:35:20.704 | INFO     | 6fa401bf314e4f80bf74a784bef857f0 | HTTP Request: GET http://*************:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16 "HTTP/1.1 200 OK"
2025-08-19 13:35:20.705 | INFO     | 6fa401bf314e4f80bf74a784bef857f0 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/87a3346677ee11f0a8f37e63526e5b16 | 18.684ms
2025-08-19 13:43:07.706 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-19 13:43:08.242 | INFO     | a371df999b264ffeb27e2663f00b8168 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:08.244 | INFO     | a371df999b264ffeb27e2663f00b8168 | 成功认证Java用户: pythontest
2025-08-19 13:43:08.272 | INFO     | a371df999b264ffeb27e2663f00b8168 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:43:08.273 | INFO     | a371df999b264ffeb27e2663f00b8168 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 41.826ms
2025-08-19 13:43:10.231 | INFO     | bd388ff306964c0ea22a7608a47af9ec | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:10.232 | INFO     | bd388ff306964c0ea22a7608a47af9ec | 成功认证Java用户: pythontest
2025-08-19 13:43:10.249 | INFO     | bd388ff306964c0ea22a7608a47af9ec | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:43:10.250 | INFO     | bd388ff306964c0ea22a7608a47af9ec | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 13:43:10.259 | INFO     | bd388ff306964c0ea22a7608a47af9ec | HTTP Request: GET http://*************:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16 "HTTP/1.1 200 OK"
2025-08-19 13:43:10.260 | INFO     | bd388ff306964c0ea22a7608a47af9ec | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/87a3346677ee11f0a8f37e63526e5b16 | 29.215ms
2025-08-19 13:43:18.961 | INFO     | 85b5b6a849db4479bd2180a98728f3e9 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:18.962 | INFO     | 85b5b6a849db4479bd2180a98728f3e9 | 成功认证Java用户: pythontest
2025-08-19 13:43:18.987 | INFO     | 85b5b6a849db4479bd2180a98728f3e9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:43:18.988 | INFO     | 85b5b6a849db4479bd2180a98728f3e9 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:43:19.003 | INFO     | 85b5b6a849db4479bd2180a98728f3e9 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:43:19.005 | INFO     | 85b5b6a849db4479bd2180a98728f3e9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 44.560ms
2025-08-19 13:43:19.635 | INFO     | 59e5c520205e4f5ab98ea8b2340f879d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:19.637 | INFO     | 59e5c520205e4f5ab98ea8b2340f879d | 成功认证Java用户: pythontest
2025-08-19 13:43:19.644 | INFO     | 59e5c520205e4f5ab98ea8b2340f879d | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:43:19.645 | INFO     | 59e5c520205e4f5ab98ea8b2340f879d | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:43:19.661 | INFO     | 59e5c520205e4f5ab98ea8b2340f879d | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:43:19.664 | INFO     | 59e5c520205e4f5ab98ea8b2340f879d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 28.992ms
2025-08-19 13:43:20.016 | INFO     | 88770995cc9d4da38d029e9559fece5d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:20.017 | INFO     | 88770995cc9d4da38d029e9559fece5d | 成功认证Java用户: pythontest
2025-08-19 13:43:20.026 | INFO     | 88770995cc9d4da38d029e9559fece5d | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:43:20.027 | INFO     | 88770995cc9d4da38d029e9559fece5d | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:43:20.047 | INFO     | 88770995cc9d4da38d029e9559fece5d | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:43:20.049 | INFO     | 88770995cc9d4da38d029e9559fece5d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 32.849ms
2025-08-19 13:43:20.281 | INFO     | c08c12c345a942dda20740bba59a9d8f | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:20.283 | INFO     | c08c12c345a942dda20740bba59a9d8f | 成功认证Java用户: pythontest
2025-08-19 13:43:20.289 | INFO     | c08c12c345a942dda20740bba59a9d8f | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:43:20.290 | INFO     | c08c12c345a942dda20740bba59a9d8f | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:43:20.306 | INFO     | c08c12c345a942dda20740bba59a9d8f | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:43:20.308 | INFO     | c08c12c345a942dda20740bba59a9d8f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 27.597ms
2025-08-19 13:43:26.837 | INFO     | ca2feecde8ed414ea6e59b5c934b9db5 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:26.838 | INFO     | ca2feecde8ed414ea6e59b5c934b9db5 | 成功认证Java用户: pythontest
2025-08-19 13:43:26.864 | INFO     | ca2feecde8ed414ea6e59b5c934b9db5 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:43:26.866 | INFO     | ca2feecde8ed414ea6e59b5c934b9db5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 29.359ms
2025-08-19 13:43:26.869 | INFO     | 8008fc4549534a1fbe7fe9baa1c8d0e4 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:26.870 | INFO     | 8008fc4549534a1fbe7fe9baa1c8d0e4 | 成功认证Java用户: pythontest
2025-08-19 13:43:26.878 | INFO     | 8008fc4549534a1fbe7fe9baa1c8d0e4 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:43:26.879 | INFO     | 8008fc4549534a1fbe7fe9baa1c8d0e4 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:43:26.896 | INFO     | bf4825ce879c46f4ba99dcc1196d4ecc | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:26.897 | INFO     | 8008fc4549534a1fbe7fe9baa1c8d0e4 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:43:26.900 | INFO     | bf4825ce879c46f4ba99dcc1196d4ecc | 成功认证Java用户: pythontest
2025-08-19 13:43:26.901 | INFO     | 8008fc4549534a1fbe7fe9baa1c8d0e4 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 33.289ms
2025-08-19 13:43:26.909 | INFO     | bf4825ce879c46f4ba99dcc1196d4ecc | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:43:26.911 | INFO     | bf4825ce879c46f4ba99dcc1196d4ecc | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:43:26.928 | INFO     | bf4825ce879c46f4ba99dcc1196d4ecc | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:43:26.930 | INFO     | bf4825ce879c46f4ba99dcc1196d4ecc | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 34.392ms
2025-08-19 13:43:33.749 | INFO     | 56176edece24443793642c5a1843f0d6 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:33.751 | INFO     | 56176edece24443793642c5a1843f0d6 | 成功认证Java用户: pythontest
2025-08-19 13:43:33.764 | INFO     | 56176edece24443793642c5a1843f0d6 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:43:33.765 | INFO     | 56176edece24443793642c5a1843f0d6 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 13:43:33.766 | INFO     | 56176edece24443793642c5a1843f0d6 | 创建知识库请求数据: {'name': 'pythontest2', 'description': '', 'permission': 'me', 'chunk_method': 'naive', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'auto_keywords': 0, 'auto_questions': 0, 'task_page_size': 12}}
2025-08-19 13:43:33.977 | INFO     | 56176edece24443793642c5a1843f0d6 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:43:33.979 | INFO     | 56176edece24443793642c5a1843f0d6 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 229.922ms
2025-08-19 13:43:33.991 | INFO     | 77190c17e3254d148183867526a7bd71 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:33.993 | INFO     | bdc2b918c1114307bb2660f7283a421d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:33.994 | INFO     | 77190c17e3254d148183867526a7bd71 | 成功认证Java用户: pythontest
2025-08-19 13:43:33.998 | INFO     | bdc2b918c1114307bb2660f7283a421d | 成功认证Java用户: pythontest
2025-08-19 13:43:34.008 | INFO     | bdc2b918c1114307bb2660f7283a421d | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:43:34.009 | INFO     | bdc2b918c1114307bb2660f7283a421d | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:43:34.021 | INFO     | 77190c17e3254d148183867526a7bd71 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:43:34.022 | INFO     | 77190c17e3254d148183867526a7bd71 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:43:34.029 | INFO     | bdc2b918c1114307bb2660f7283a421d | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:43:34.030 | INFO     | bdc2b918c1114307bb2660f7283a421d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 39.055ms
2025-08-19 13:43:34.039 | INFO     | 77190c17e3254d148183867526a7bd71 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:43:34.041 | INFO     | 77190c17e3254d148183867526a7bd71 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 50.646ms
2025-08-19 13:43:39.397 | INFO     | 7bb59e5eb8d542039d333778fd3fee12 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:39.399 | INFO     | 7bb59e5eb8d542039d333778fd3fee12 | 成功认证Java用户: pythontest
2025-08-19 13:43:39.408 | INFO     | 7bb59e5eb8d542039d333778fd3fee12 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:43:39.409 | INFO     | 7bb59e5eb8d542039d333778fd3fee12 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-19 13:43:39.427 | INFO     | 7bb59e5eb8d542039d333778fd3fee12 | HTTP Request: PUT http://*************:6610/api/v1/datasets/721d81667cbf11f09259ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 13:43:39.429 | INFO     | 7bb59e5eb8d542039d333778fd3fee12 | 127.0.0.1       | PUT      | 200    | /api/iot/v1/knowledge-base/721d81667cbf11f09259ea5dc8d5776c | 32.888ms
2025-08-19 13:45:11.077 | INFO     | 25eb588450774b21a407dcf4c02ee965 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:11.078 | INFO     | 7e25b9f9534e45f1ad2b0431990b8c61 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:11.078 | INFO     | 9afae2d5d12248d0b7cb577333e139d6 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:11.081 | INFO     | 25eb588450774b21a407dcf4c02ee965 | 成功认证Java用户: pythontest
2025-08-19 13:45:11.081 | INFO     | 7e25b9f9534e45f1ad2b0431990b8c61 | 成功认证Java用户: pythontest
2025-08-19 13:45:11.083 | INFO     | 9afae2d5d12248d0b7cb577333e139d6 | 成功认证Java用户: pythontest
2025-08-19 13:45:11.092 | INFO     | 9afae2d5d12248d0b7cb577333e139d6 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:45:11.093 | INFO     | 9afae2d5d12248d0b7cb577333e139d6 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:45:11.096 | INFO     | 7e25b9f9534e45f1ad2b0431990b8c61 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:45:11.097 | INFO     | 7e25b9f9534e45f1ad2b0431990b8c61 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:45:11.101 | INFO     | 25eb588450774b21a407dcf4c02ee965 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:45:11.104 | INFO     | 25eb588450774b21a407dcf4c02ee965 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 26.809ms
2025-08-19 13:45:11.122 | INFO     | 9afae2d5d12248d0b7cb577333e139d6 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:45:11.123 | INFO     | 7e25b9f9534e45f1ad2b0431990b8c61 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:45:11.125 | INFO     | 9afae2d5d12248d0b7cb577333e139d6 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 45.799ms
2025-08-19 13:45:11.126 | INFO     | 7e25b9f9534e45f1ad2b0431990b8c61 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 47.625ms
2025-08-19 13:45:24.130 | INFO     | 5002fe6a769c49bba7614485616372b8 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:24.131 | INFO     | 5002fe6a769c49bba7614485616372b8 | 成功认证Java用户: pythontest
2025-08-19 13:45:24.146 | INFO     | 5002fe6a769c49bba7614485616372b8 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:45:24.147 | INFO     | 5002fe6a769c49bba7614485616372b8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 16.855ms
2025-08-19 13:45:24.149 | INFO     | 8746c97dce8d43aa8ff5d401c958aec9 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:24.150 | INFO     | 8746c97dce8d43aa8ff5d401c958aec9 | 成功认证Java用户: pythontest
2025-08-19 13:45:24.156 | INFO     | 8746c97dce8d43aa8ff5d401c958aec9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:45:24.157 | INFO     | 8746c97dce8d43aa8ff5d401c958aec9 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:45:24.173 | INFO     | 8746c97dce8d43aa8ff5d401c958aec9 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:45:24.174 | INFO     | 8746c97dce8d43aa8ff5d401c958aec9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 25.170ms
2025-08-19 13:45:24.177 | INFO     | 7f28d9665da84c02b03ddf0eb100a563 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:24.178 | INFO     | 7f28d9665da84c02b03ddf0eb100a563 | 成功认证Java用户: pythontest
2025-08-19 13:45:24.184 | INFO     | 7f28d9665da84c02b03ddf0eb100a563 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:45:24.184 | INFO     | 7f28d9665da84c02b03ddf0eb100a563 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:45:24.199 | INFO     | 7f28d9665da84c02b03ddf0eb100a563 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:45:24.200 | INFO     | 7f28d9665da84c02b03ddf0eb100a563 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 23.558ms
2025-08-19 13:45:34.080 | INFO     | 6ef4d9066a5e4d4b95bab1749fa6bdf5 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:34.081 | INFO     | 23925fcea89b41a09b3dbdbfd8572fdd | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:34.081 | INFO     | 6ef4d9066a5e4d4b95bab1749fa6bdf5 | 成功认证Java用户: pythontest
2025-08-19 13:45:34.082 | INFO     | 23925fcea89b41a09b3dbdbfd8572fdd | 成功认证Java用户: pythontest
2025-08-19 13:45:34.090 | INFO     | 23925fcea89b41a09b3dbdbfd8572fdd | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:45:34.091 | INFO     | 23925fcea89b41a09b3dbdbfd8572fdd | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:45:34.098 | INFO     | 6ef4d9066a5e4d4b95bab1749fa6bdf5 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:45:34.099 | INFO     | 6ef4d9066a5e4d4b95bab1749fa6bdf5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 18.974ms
2025-08-19 13:45:34.100 | INFO     | 00749d18b8004ef1a86948fe4b888067 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:34.102 | INFO     | 00749d18b8004ef1a86948fe4b888067 | 成功认证Java用户: pythontest
2025-08-19 13:45:34.106 | INFO     | 23925fcea89b41a09b3dbdbfd8572fdd | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:45:34.107 | INFO     | 00749d18b8004ef1a86948fe4b888067 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:45:34.108 | INFO     | 00749d18b8004ef1a86948fe4b888067 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:45:34.111 | INFO     | 23925fcea89b41a09b3dbdbfd8572fdd | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 30.834ms
2025-08-19 13:45:34.124 | INFO     | 00749d18b8004ef1a86948fe4b888067 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:45:34.125 | INFO     | 00749d18b8004ef1a86948fe4b888067 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 24.981ms
2025-08-19 13:45:56.765 | INFO     | 986106df236f4ef79a6bd93cfad9fba2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:56.766 | INFO     | 986106df236f4ef79a6bd93cfad9fba2 | 成功认证Java用户: pythontest
2025-08-19 13:45:56.781 | INFO     | 986106df236f4ef79a6bd93cfad9fba2 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:45:56.782 | INFO     | 986106df236f4ef79a6bd93cfad9fba2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 17.598ms
2025-08-19 13:45:56.785 | INFO     | f2505917b72d499f9c9b5a17bfc2e974 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:56.786 | INFO     | f2505917b72d499f9c9b5a17bfc2e974 | 成功认证Java用户: pythontest
2025-08-19 13:45:56.793 | INFO     | f2505917b72d499f9c9b5a17bfc2e974 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:45:56.794 | INFO     | f2505917b72d499f9c9b5a17bfc2e974 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:45:56.810 | INFO     | f2505917b72d499f9c9b5a17bfc2e974 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:45:56.811 | INFO     | f2505917b72d499f9c9b5a17bfc2e974 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 26.792ms
2025-08-19 13:45:56.814 | INFO     | 381a3787d62b463f9bef1eb9c84188ab | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:56.815 | INFO     | 381a3787d62b463f9bef1eb9c84188ab | 成功认证Java用户: pythontest
2025-08-19 13:45:56.822 | INFO     | 381a3787d62b463f9bef1eb9c84188ab | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:45:56.822 | INFO     | 381a3787d62b463f9bef1eb9c84188ab | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:45:56.839 | INFO     | 381a3787d62b463f9bef1eb9c84188ab | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:45:56.840 | INFO     | 381a3787d62b463f9bef1eb9c84188ab | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 26.446ms
2025-08-19 13:46:10.514 | INFO     | 10637e5a44e94ccdb48f7e4fb8a2bf71 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:10.516 | INFO     | fb15b2be1b3a496d8f4a49358fecfc92 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:10.517 | INFO     | 10637e5a44e94ccdb48f7e4fb8a2bf71 | 成功认证Java用户: pythontest
2025-08-19 13:46:10.519 | INFO     | fb15b2be1b3a496d8f4a49358fecfc92 | 成功认证Java用户: pythontest
2025-08-19 13:46:10.525 | INFO     | fb15b2be1b3a496d8f4a49358fecfc92 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:46:10.526 | INFO     | fb15b2be1b3a496d8f4a49358fecfc92 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:46:10.531 | INFO     | 10637e5a44e94ccdb48f7e4fb8a2bf71 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:46:10.532 | INFO     | 10637e5a44e94ccdb48f7e4fb8a2bf71 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 18.017ms
2025-08-19 13:46:10.534 | INFO     | deeb90e229194b41b447d2abc5a7406f | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:10.536 | INFO     | deeb90e229194b41b447d2abc5a7406f | 成功认证Java用户: pythontest
2025-08-19 13:46:10.539 | INFO     | fb15b2be1b3a496d8f4a49358fecfc92 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:46:10.541 | INFO     | fb15b2be1b3a496d8f4a49358fecfc92 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 25.809ms
2025-08-19 13:46:10.542 | INFO     | deeb90e229194b41b447d2abc5a7406f | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:46:10.543 | INFO     | deeb90e229194b41b447d2abc5a7406f | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:46:10.559 | INFO     | deeb90e229194b41b447d2abc5a7406f | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:46:10.560 | INFO     | deeb90e229194b41b447d2abc5a7406f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 26.265ms
2025-08-19 13:46:22.563 | INFO     | 5ac3f4a2c1454ecb8ecf4f9e48469758 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:22.564 | INFO     | 5ac3f4a2c1454ecb8ecf4f9e48469758 | 成功认证Java用户: pythontest
2025-08-19 13:46:22.578 | INFO     | 5ac3f4a2c1454ecb8ecf4f9e48469758 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:46:22.579 | INFO     | 5ac3f4a2c1454ecb8ecf4f9e48469758 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 16.288ms
2025-08-19 13:46:22.581 | INFO     | 8af8322b87c0480184dd50401073f88c | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:22.583 | INFO     | 8af8322b87c0480184dd50401073f88c | 成功认证Java用户: pythontest
2025-08-19 13:46:22.589 | INFO     | 8af8322b87c0480184dd50401073f88c | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:46:22.589 | INFO     | 8af8322b87c0480184dd50401073f88c | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:46:22.603 | INFO     | 8af8322b87c0480184dd50401073f88c | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:46:22.604 | INFO     | 8af8322b87c0480184dd50401073f88c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 22.991ms
2025-08-19 13:46:22.606 | INFO     | 6978ea26abfc48b99a11e5d8423c546a | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:22.607 | INFO     | 6978ea26abfc48b99a11e5d8423c546a | 成功认证Java用户: pythontest
2025-08-19 13:46:22.612 | INFO     | 6978ea26abfc48b99a11e5d8423c546a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:46:22.613 | INFO     | 6978ea26abfc48b99a11e5d8423c546a | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:46:22.626 | INFO     | 6978ea26abfc48b99a11e5d8423c546a | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:46:22.627 | INFO     | 6978ea26abfc48b99a11e5d8423c546a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 21.489ms
2025-08-19 13:46:35.034 | INFO     | 6a96752e53294aecbcc471bfe9c20cdf | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:35.035 | INFO     | 1a8f72ed520f4f75b6c27225b8d880b9 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:35.036 | INFO     | 6a96752e53294aecbcc471bfe9c20cdf | 成功认证Java用户: pythontest
2025-08-19 13:46:35.037 | INFO     | 1a8f72ed520f4f75b6c27225b8d880b9 | 成功认证Java用户: pythontest
2025-08-19 13:46:35.047 | INFO     | 1a8f72ed520f4f75b6c27225b8d880b9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:46:35.048 | INFO     | 1a8f72ed520f4f75b6c27225b8d880b9 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:46:35.056 | INFO     | 6a96752e53294aecbcc471bfe9c20cdf | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:46:35.057 | INFO     | 6a96752e53294aecbcc471bfe9c20cdf | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 23.597ms
2025-08-19 13:46:35.061 | INFO     | eb118cf64c23461f84e312aebfee8d9d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:35.063 | INFO     | eb118cf64c23461f84e312aebfee8d9d | 成功认证Java用户: pythontest
2025-08-19 13:46:35.064 | INFO     | 1a8f72ed520f4f75b6c27225b8d880b9 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:46:35.067 | INFO     | 1a8f72ed520f4f75b6c27225b8d880b9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 31.882ms
2025-08-19 13:46:35.073 | INFO     | eb118cf64c23461f84e312aebfee8d9d | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:46:35.074 | INFO     | eb118cf64c23461f84e312aebfee8d9d | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:46:35.090 | INFO     | eb118cf64c23461f84e312aebfee8d9d | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:46:35.091 | INFO     | eb118cf64c23461f84e312aebfee8d9d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 30.738ms
2025-08-19 13:46:46.000 | INFO     | 8d4e0fea63be4d7998cce54337d5120b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:46.002 | INFO     | 8d4e0fea63be4d7998cce54337d5120b | 成功认证Java用户: pythontest
2025-08-19 13:46:46.015 | INFO     | 8d4e0fea63be4d7998cce54337d5120b | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:46:46.017 | INFO     | 8d4e0fea63be4d7998cce54337d5120b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 16.788ms
2025-08-19 13:46:46.019 | INFO     | c083816ac9fe4ac48df0de3839bca114 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:46.020 | INFO     | c083816ac9fe4ac48df0de3839bca114 | 成功认证Java用户: pythontest
2025-08-19 13:46:46.026 | INFO     | c083816ac9fe4ac48df0de3839bca114 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:46:46.026 | INFO     | c083816ac9fe4ac48df0de3839bca114 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:46:46.041 | INFO     | c083816ac9fe4ac48df0de3839bca114 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:46:46.042 | INFO     | c083816ac9fe4ac48df0de3839bca114 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 22.619ms
2025-08-19 13:46:46.044 | INFO     | 279ebdf7798c44458987215a697dec6e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:46.046 | INFO     | 279ebdf7798c44458987215a697dec6e | 成功认证Java用户: pythontest
2025-08-19 13:46:46.050 | INFO     | 279ebdf7798c44458987215a697dec6e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:46:46.051 | INFO     | 279ebdf7798c44458987215a697dec6e | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:46:46.065 | INFO     | 279ebdf7798c44458987215a697dec6e | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:46:46.066 | INFO     | 279ebdf7798c44458987215a697dec6e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 22.450ms
2025-08-19 13:46:54.163 | INFO     | 0eae73de9a074a2b9d657dbdee5cd7f4 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:54.165 | INFO     | 1fff5d0cce1e41fe80e1dbbe1e8bbae1 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:54.165 | INFO     | 0eae73de9a074a2b9d657dbdee5cd7f4 | 成功认证Java用户: pythontest
2025-08-19 13:46:54.166 | INFO     | 1fff5d0cce1e41fe80e1dbbe1e8bbae1 | 成功认证Java用户: pythontest
2025-08-19 13:46:54.173 | INFO     | 1fff5d0cce1e41fe80e1dbbe1e8bbae1 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:46:54.173 | INFO     | 1fff5d0cce1e41fe80e1dbbe1e8bbae1 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:46:54.179 | INFO     | 0eae73de9a074a2b9d657dbdee5cd7f4 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:46:54.180 | INFO     | 0eae73de9a074a2b9d657dbdee5cd7f4 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 16.720ms
2025-08-19 13:46:54.183 | INFO     | 96ad188349144ddc99e3ca8c0094c00b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:54.184 | INFO     | 96ad188349144ddc99e3ca8c0094c00b | 成功认证Java用户: pythontest
2025-08-19 13:46:54.187 | INFO     | 1fff5d0cce1e41fe80e1dbbe1e8bbae1 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:46:54.189 | INFO     | 1fff5d0cce1e41fe80e1dbbe1e8bbae1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 23.657ms
2025-08-19 13:46:54.190 | INFO     | 96ad188349144ddc99e3ca8c0094c00b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:46:54.190 | INFO     | 96ad188349144ddc99e3ca8c0094c00b | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:46:54.205 | INFO     | 96ad188349144ddc99e3ca8c0094c00b | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:46:54.206 | INFO     | 96ad188349144ddc99e3ca8c0094c00b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 24.160ms
2025-08-19 13:57:37.848 | INFO     | 23eeb68762344b4e9e6af9ec4908480f | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:57:37.851 | INFO     | b4784494991f4b688f32fa2f3826eecd | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:57:37.853 | INFO     | 23eeb68762344b4e9e6af9ec4908480f | 成功认证Java用户: pythontest
2025-08-19 13:57:37.853 | INFO     | b4784494991f4b688f32fa2f3826eecd | 成功认证Java用户: pythontest
2025-08-19 13:57:37.862 | INFO     | b4784494991f4b688f32fa2f3826eecd | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:57:37.863 | INFO     | b4784494991f4b688f32fa2f3826eecd | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:57:37.868 | INFO     | 23eeb68762344b4e9e6af9ec4908480f | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:57:37.870 | INFO     | 23eeb68762344b4e9e6af9ec4908480f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 21.348ms
2025-08-19 13:57:37.871 | INFO     | a39e16d3ee6c49a692af7da3ec0262a0 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:57:37.872 | INFO     | a39e16d3ee6c49a692af7da3ec0262a0 | 成功认证Java用户: pythontest
2025-08-19 13:57:37.876 | INFO     | b4784494991f4b688f32fa2f3826eecd | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:57:37.877 | INFO     | a39e16d3ee6c49a692af7da3ec0262a0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:57:37.877 | INFO     | a39e16d3ee6c49a692af7da3ec0262a0 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:57:37.881 | INFO     | b4784494991f4b688f32fa2f3826eecd | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 28.817ms
2025-08-19 13:57:37.893 | INFO     | a39e16d3ee6c49a692af7da3ec0262a0 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:57:37.895 | INFO     | a39e16d3ee6c49a692af7da3ec0262a0 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 23.648ms
2025-08-19 13:57:39.399 | INFO     | ed92382461dd482f85f90509e5c005f0 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:57:39.401 | INFO     | ed92382461dd482f85f90509e5c005f0 | 成功认证Java用户: pythontest
2025-08-19 13:57:39.406 | INFO     | ed92382461dd482f85f90509e5c005f0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:57:39.407 | INFO     | ed92382461dd482f85f90509e5c005f0 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:57:39.421 | INFO     | ed92382461dd482f85f90509e5c005f0 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:57:39.422 | INFO     | ed92382461dd482f85f90509e5c005f0 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 22.630ms
2025-08-19 13:57:40.293 | INFO     | 0307855735034efab4e0898322deb6f8 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:57:40.295 | INFO     | 0307855735034efab4e0898322deb6f8 | 成功认证Java用户: pythontest
2025-08-19 13:57:40.301 | INFO     | 0307855735034efab4e0898322deb6f8 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:57:40.301 | INFO     | 0307855735034efab4e0898322deb6f8 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 13:57:40.313 | INFO     | 0307855735034efab4e0898322deb6f8 | HTTP Request: GET http://*************:6610/api/v1/datasets/721d81667cbf11f09259ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 13:57:40.315 | INFO     | 0307855735034efab4e0898322deb6f8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/721d81667cbf11f09259ea5dc8d5776c | 21.784ms
2025-08-19 13:57:41.182 | INFO     | 641723bb35614034a025e0a1858a323e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:57:41.184 | INFO     | 641723bb35614034a025e0a1858a323e | 成功认证Java用户: pythontest
2025-08-19 13:57:41.190 | INFO     | 641723bb35614034a025e0a1858a323e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:57:41.191 | INFO     | 641723bb35614034a025e0a1858a323e | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 13:57:41.202 | INFO     | 641723bb35614034a025e0a1858a323e | HTTP Request: GET http://*************:6610/api/v1/datasets/721d81667cbf11f09259ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 13:57:41.204 | INFO     | 641723bb35614034a025e0a1858a323e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/721d81667cbf11f09259ea5dc8d5776c | 22.822ms
2025-08-19 13:57:41.604 | INFO     | 048d5f724c6e474bbfb9593e6389f642 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:57:41.606 | INFO     | 048d5f724c6e474bbfb9593e6389f642 | 成功认证Java用户: pythontest
2025-08-19 13:57:41.611 | INFO     | 048d5f724c6e474bbfb9593e6389f642 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:57:41.612 | INFO     | 048d5f724c6e474bbfb9593e6389f642 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 13:57:41.623 | INFO     | 048d5f724c6e474bbfb9593e6389f642 | HTTP Request: GET http://*************:6610/api/v1/datasets/721d81667cbf11f09259ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 13:57:41.625 | INFO     | 048d5f724c6e474bbfb9593e6389f642 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/721d81667cbf11f09259ea5dc8d5776c | 21.769ms
2025-08-19 13:58:05.818 | INFO     | aaf59d25659948fd88414d6b17474bb1 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:58:05.820 | INFO     | aaf59d25659948fd88414d6b17474bb1 | 成功认证Java用户: pythontest
2025-08-19 13:58:05.827 | INFO     | aaf59d25659948fd88414d6b17474bb1 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:58:05.828 | INFO     | aaf59d25659948fd88414d6b17474bb1 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-19 13:58:05.992 | INFO     | aaf59d25659948fd88414d6b17474bb1 | HTTP Request: PUT http://*************:6610/api/v1/datasets/721d81667cbf11f09259ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 13:58:05.994 | INFO     | aaf59d25659948fd88414d6b17474bb1 | 127.0.0.1       | PUT      | 200    | /api/iot/v1/knowledge-base/721d81667cbf11f09259ea5dc8d5776c | 174.963ms
2025-08-19 13:58:06.004 | INFO     | 16157b69cbba4ed394a29655796987d2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:58:06.006 | INFO     | 002b38fa7b9449eb89a2a0979717496e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:58:06.007 | INFO     | 16157b69cbba4ed394a29655796987d2 | 成功认证Java用户: pythontest
2025-08-19 13:58:06.008 | INFO     | 002b38fa7b9449eb89a2a0979717496e | 成功认证Java用户: pythontest
2025-08-19 13:58:06.016 | INFO     | 002b38fa7b9449eb89a2a0979717496e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:58:06.018 | INFO     | 002b38fa7b9449eb89a2a0979717496e | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:58:06.023 | INFO     | 16157b69cbba4ed394a29655796987d2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:58:06.025 | INFO     | 16157b69cbba4ed394a29655796987d2 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:58:06.050 | INFO     | 002b38fa7b9449eb89a2a0979717496e | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:58:06.051 | INFO     | 16157b69cbba4ed394a29655796987d2 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:58:06.054 | INFO     | 002b38fa7b9449eb89a2a0979717496e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 49.466ms
2025-08-19 13:58:06.056 | INFO     | 16157b69cbba4ed394a29655796987d2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 52.636ms
2025-08-19 13:58:09.359 | INFO     | 9d849bdd43f243c5ab93f0e3cbbd255d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:58:09.362 | INFO     | 9d849bdd43f243c5ab93f0e3cbbd255d | 成功认证Java用户: pythontest
2025-08-19 13:58:09.369 | INFO     | 9d849bdd43f243c5ab93f0e3cbbd255d | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:58:09.370 | INFO     | 9d849bdd43f243c5ab93f0e3cbbd255d | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:58:09.385 | INFO     | 9d849bdd43f243c5ab93f0e3cbbd255d | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:58:09.387 | INFO     | 9d849bdd43f243c5ab93f0e3cbbd255d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 27.596ms
2025-08-19 13:58:09.983 | INFO     | bb34049d74c944dab5753ccf4e73533b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:58:09.985 | INFO     | bb34049d74c944dab5753ccf4e73533b | 成功认证Java用户: pythontest
2025-08-19 13:58:09.992 | INFO     | bb34049d74c944dab5753ccf4e73533b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:58:09.993 | INFO     | bb34049d74c944dab5753ccf4e73533b | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:58:10.010 | INFO     | bb34049d74c944dab5753ccf4e73533b | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:58:10.012 | INFO     | bb34049d74c944dab5753ccf4e73533b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 29.397ms
2025-08-19 13:58:10.248 | INFO     | 3ee6ddff03d947788476117f4440c457 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:58:10.250 | INFO     | 3ee6ddff03d947788476117f4440c457 | 成功认证Java用户: pythontest
2025-08-19 13:58:10.258 | INFO     | 3ee6ddff03d947788476117f4440c457 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:58:10.259 | INFO     | 3ee6ddff03d947788476117f4440c457 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:58:10.275 | INFO     | 3ee6ddff03d947788476117f4440c457 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:58:10.277 | INFO     | 3ee6ddff03d947788476117f4440c457 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 28.904ms
2025-08-19 13:58:10.390 | INFO     | c0238193f7d34e37804eecfb76949aa8 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:58:10.393 | INFO     | c0238193f7d34e37804eecfb76949aa8 | 成功认证Java用户: pythontest
2025-08-19 13:58:10.399 | INFO     | c0238193f7d34e37804eecfb76949aa8 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:58:10.400 | INFO     | c0238193f7d34e37804eecfb76949aa8 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:58:10.416 | INFO     | c0238193f7d34e37804eecfb76949aa8 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:58:10.417 | INFO     | c0238193f7d34e37804eecfb76949aa8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 27.535ms
2025-08-19 13:58:39.032 | INFO     | 457fe1877e004d5f8a7bdbcfbb0593fb | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:58:39.033 | INFO     | 457fe1877e004d5f8a7bdbcfbb0593fb | 成功认证Java用户: pythontest
2025-08-19 13:58:39.037 | INFO     | 457fe1877e004d5f8a7bdbcfbb0593fb | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:58:39.037 | INFO     | 457fe1877e004d5f8a7bdbcfbb0593fb | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 13:58:39.047 | INFO     | 457fe1877e004d5f8a7bdbcfbb0593fb | HTTP Request: GET http://*************:6610/api/v1/datasets/721d81667cbf11f09259ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 13:58:39.049 | INFO     | 457fe1877e004d5f8a7bdbcfbb0593fb | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/721d81667cbf11f09259ea5dc8d5776c | 17.532ms
2025-08-19 13:58:58.326 | INFO     | 21584e01793e4161b4afa3cb0e5ed8e8 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:58:58.328 | INFO     | 21584e01793e4161b4afa3cb0e5ed8e8 | 成功认证Java用户: pythontest
2025-08-19 13:58:58.343 | INFO     | 21584e01793e4161b4afa3cb0e5ed8e8 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:58:58.344 | INFO     | 21584e01793e4161b4afa3cb0e5ed8e8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 17.939ms
2025-08-19 13:59:07.229 | INFO     | 12107605238c4fcaabe96871d959ed24 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:59:07.231 | INFO     | 12107605238c4fcaabe96871d959ed24 | 成功认证Java用户: pythontest
2025-08-19 13:59:07.237 | INFO     | 12107605238c4fcaabe96871d959ed24 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:59:07.238 | INFO     | 12107605238c4fcaabe96871d959ed24 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 13:59:07.238 | INFO     | 12107605238c4fcaabe96871d959ed24 | 创建知识库请求数据: {'name': 'pythontest3', 'description': ''}
2025-08-19 13:59:07.405 | INFO     | 12107605238c4fcaabe96871d959ed24 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:59:07.407 | INFO     | 12107605238c4fcaabe96871d959ed24 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 178.004ms
2025-08-19 13:59:07.417 | INFO     | 100a93be0a034e6ea90df8b1ceb98e34 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:59:07.419 | INFO     | 100a93be0a034e6ea90df8b1ceb98e34 | 成功认证Java用户: pythontest
2025-08-19 13:59:07.426 | INFO     | 100a93be0a034e6ea90df8b1ceb98e34 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:59:07.426 | INFO     | 100a93be0a034e6ea90df8b1ceb98e34 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:59:07.442 | INFO     | 100a93be0a034e6ea90df8b1ceb98e34 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:59:07.443 | INFO     | 100a93be0a034e6ea90df8b1ceb98e34 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 25.461ms
2025-08-19 13:59:07.445 | INFO     | aec14e624ad74a05916ff470f9b852f2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:59:07.446 | INFO     | aec14e624ad74a05916ff470f9b852f2 | 成功认证Java用户: pythontest
2025-08-19 13:59:07.454 | INFO     | aec14e624ad74a05916ff470f9b852f2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:59:07.455 | INFO     | aec14e624ad74a05916ff470f9b852f2 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:59:07.480 | INFO     | aec14e624ad74a05916ff470f9b852f2 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:59:07.483 | INFO     | aec14e624ad74a05916ff470f9b852f2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 37.718ms
2025-08-19 13:59:09.944 | INFO     | a949326dae99410b955cd17daff743cd | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:59:09.945 | INFO     | a949326dae99410b955cd17daff743cd | 成功认证Java用户: pythontest
2025-08-19 13:59:09.950 | INFO     | a949326dae99410b955cd17daff743cd | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:59:09.951 | INFO     | a949326dae99410b955cd17daff743cd | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 13:59:09.961 | INFO     | a949326dae99410b955cd17daff743cd | HTTP Request: GET http://*************:6610/api/v1/datasets/9e8389ba7cc111f0a6f9ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 13:59:09.962 | INFO     | a949326dae99410b955cd17daff743cd | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/9e8389ba7cc111f0a6f9ea5dc8d5776c | 18.417ms
2025-08-19 13:59:14.764 | INFO     | c8a697c4e5ac4840a7e5c1f36d363bd4 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:59:14.766 | INFO     | c8a697c4e5ac4840a7e5c1f36d363bd4 | 成功认证Java用户: pythontest
2025-08-19 13:59:14.773 | INFO     | c8a697c4e5ac4840a7e5c1f36d363bd4 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:59:14.774 | INFO     | c8a697c4e5ac4840a7e5c1f36d363bd4 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-19 13:59:14.896 | INFO     | c8a697c4e5ac4840a7e5c1f36d363bd4 | HTTP Request: PUT http://*************:6610/api/v1/datasets/9e8389ba7cc111f0a6f9ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 13:59:14.897 | INFO     | c8a697c4e5ac4840a7e5c1f36d363bd4 | 127.0.0.1       | PUT      | 200    | /api/iot/v1/knowledge-base/9e8389ba7cc111f0a6f9ea5dc8d5776c | 133.129ms
2025-08-19 13:59:14.907 | INFO     | ff5e38eb89da4c659a7328d4b5b0b6b3 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:59:14.908 | INFO     | e219e429ff5b43d6bba30ed5784ca25a | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:59:14.908 | INFO     | ff5e38eb89da4c659a7328d4b5b0b6b3 | 成功认证Java用户: pythontest
2025-08-19 13:59:14.910 | INFO     | e219e429ff5b43d6bba30ed5784ca25a | 成功认证Java用户: pythontest
2025-08-19 13:59:14.915 | INFO     | ff5e38eb89da4c659a7328d4b5b0b6b3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:59:14.915 | INFO     | ff5e38eb89da4c659a7328d4b5b0b6b3 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:59:14.920 | INFO     | e219e429ff5b43d6bba30ed5784ca25a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:59:14.920 | INFO     | e219e429ff5b43d6bba30ed5784ca25a | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:59:14.931 | INFO     | ff5e38eb89da4c659a7328d4b5b0b6b3 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:59:14.932 | INFO     | ff5e38eb89da4c659a7328d4b5b0b6b3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 25.048ms
2025-08-19 13:59:14.936 | INFO     | e219e429ff5b43d6bba30ed5784ca25a | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:59:14.938 | INFO     | e219e429ff5b43d6bba30ed5784ca25a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 29.895ms
2025-08-19 14:00:23.816 | INFO     | 923dbfc3315c4e36b0e0492c193e64d6 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:00:23.817 | INFO     | c96931499cb24fa1aeaabd969cb2ce2d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:00:23.818 | INFO     | 97388a5c65624a909614096839b5ad4d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:00:23.819 | INFO     | 923dbfc3315c4e36b0e0492c193e64d6 | 成功认证Java用户: pythontest
2025-08-19 14:00:23.820 | INFO     | c96931499cb24fa1aeaabd969cb2ce2d | 成功认证Java用户: pythontest
2025-08-19 14:00:23.821 | INFO     | 97388a5c65624a909614096839b5ad4d | 成功认证Java用户: pythontest
2025-08-19 14:00:23.831 | INFO     | 97388a5c65624a909614096839b5ad4d | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:00:23.833 | INFO     | 97388a5c65624a909614096839b5ad4d | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:00:23.838 | INFO     | c96931499cb24fa1aeaabd969cb2ce2d | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:00:23.838 | INFO     | c96931499cb24fa1aeaabd969cb2ce2d | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:00:23.843 | INFO     | 923dbfc3315c4e36b0e0492c193e64d6 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:00:23.847 | INFO     | 923dbfc3315c4e36b0e0492c193e64d6 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 31.220ms
2025-08-19 14:00:23.862 | INFO     | 97388a5c65624a909614096839b5ad4d | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:00:23.863 | INFO     | c96931499cb24fa1aeaabd969cb2ce2d | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:00:23.865 | INFO     | 97388a5c65624a909614096839b5ad4d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 48.139ms
2025-08-19 14:00:23.867 | INFO     | c96931499cb24fa1aeaabd969cb2ce2d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 50.535ms
2025-08-19 14:04:37.995 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-19 14:04:41.493 | INFO     | 1d9ef97ef8dc43ce95302f12a014f59a | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:04:41.494 | INFO     | f8a9321472b843bfb9d6188ea3b659a9 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:04:41.495 | INFO     | 4c6b99e0c9274aaab602b67ccbb16307 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:04:41.496 | INFO     | 1d9ef97ef8dc43ce95302f12a014f59a | 成功认证Java用户: pythontest
2025-08-19 14:04:41.498 | INFO     | f8a9321472b843bfb9d6188ea3b659a9 | 成功认证Java用户: pythontest
2025-08-19 14:04:41.498 | INFO     | 4c6b99e0c9274aaab602b67ccbb16307 | 成功认证Java用户: pythontest
2025-08-19 14:04:41.524 | INFO     | f8a9321472b843bfb9d6188ea3b659a9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:04:41.525 | INFO     | f8a9321472b843bfb9d6188ea3b659a9 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:04:41.528 | INFO     | 1d9ef97ef8dc43ce95302f12a014f59a | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:04:41.531 | INFO     | 4c6b99e0c9274aaab602b67ccbb16307 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:04:41.531 | INFO     | 4c6b99e0c9274aaab602b67ccbb16307 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:04:41.537 | INFO     | 1d9ef97ef8dc43ce95302f12a014f59a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 50.744ms
2025-08-19 14:04:41.550 | INFO     | f8a9321472b843bfb9d6188ea3b659a9 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:04:41.552 | INFO     | f8a9321472b843bfb9d6188ea3b659a9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 59.148ms
2025-08-19 14:04:41.553 | INFO     | 4c6b99e0c9274aaab602b67ccbb16307 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:04:41.555 | INFO     | 4c6b99e0c9274aaab602b67ccbb16307 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 62.209ms
2025-08-19 14:04:42.440 | INFO     | 4acc9735a15f40869b03becc282679ef | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:04:42.441 | INFO     | 4acc9735a15f40869b03becc282679ef | 成功认证Java用户: pythontest
2025-08-19 14:04:42.463 | INFO     | 4acc9735a15f40869b03becc282679ef | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:04:42.464 | INFO     | 4acc9735a15f40869b03becc282679ef | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 14:04:42.480 | INFO     | 4acc9735a15f40869b03becc282679ef | HTTP Request: GET http://*************:6610/api/v1/datasets?id=9e8389ba7cc111f0a6f9ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 14:04:42.481 | INFO     | 4acc9735a15f40869b03becc282679ef | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/9e8389ba7cc111f0a6f9ea5dc8d5776c | 41.039ms
2025-08-19 14:04:46.989 | INFO     | 2cb4f09071b54cf298f0c0ea5e4a8ea0 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:04:46.991 | INFO     | 2cb4f09071b54cf298f0c0ea5e4a8ea0 | 成功认证Java用户: pythontest
2025-08-19 14:04:46.997 | INFO     | 2cb4f09071b54cf298f0c0ea5e4a8ea0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:04:46.998 | INFO     | 2cb4f09071b54cf298f0c0ea5e4a8ea0 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 14:04:47.015 | INFO     | 2cb4f09071b54cf298f0c0ea5e4a8ea0 | HTTP Request: GET http://*************:6610/api/v1/datasets?id=9e8389ba7cc111f0a6f9ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 14:04:47.017 | INFO     | 2cb4f09071b54cf298f0c0ea5e4a8ea0 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/9e8389ba7cc111f0a6f9ea5dc8d5776c | 27.777ms
2025-08-19 14:05:01.382 | INFO     | fbbf0481b9fa49fb940dbf8dc40b1f08 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:05:01.384 | INFO     | fbbf0481b9fa49fb940dbf8dc40b1f08 | 成功认证Java用户: pythontest
2025-08-19 14:05:01.393 | INFO     | fbbf0481b9fa49fb940dbf8dc40b1f08 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:05:01.393 | INFO     | fbbf0481b9fa49fb940dbf8dc40b1f08 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-19 14:05:01.515 | INFO     | fbbf0481b9fa49fb940dbf8dc40b1f08 | HTTP Request: PUT http://*************:6610/api/v1/datasets/9e8389ba7cc111f0a6f9ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 14:05:01.517 | INFO     | fbbf0481b9fa49fb940dbf8dc40b1f08 | 127.0.0.1       | PUT      | 200    | /api/iot/v1/knowledge-base/9e8389ba7cc111f0a6f9ea5dc8d5776c | 134.380ms
2025-08-19 14:05:01.529 | INFO     | 77965396c8894743b615683e4dc96840 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:05:01.531 | INFO     | 77965396c8894743b615683e4dc96840 | 成功认证Java用户: pythontest
2025-08-19 14:05:01.540 | INFO     | 77965396c8894743b615683e4dc96840 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:05:01.542 | INFO     | 77965396c8894743b615683e4dc96840 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:05:01.562 | INFO     | 77965396c8894743b615683e4dc96840 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:05:01.564 | INFO     | 77965396c8894743b615683e4dc96840 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 35.398ms
2025-08-19 14:05:01.567 | INFO     | 325107949cab4e9fbf33980349010e66 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:05:01.569 | INFO     | 325107949cab4e9fbf33980349010e66 | 成功认证Java用户: pythontest
2025-08-19 14:05:01.579 | INFO     | 325107949cab4e9fbf33980349010e66 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:05:01.580 | INFO     | 325107949cab4e9fbf33980349010e66 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:05:01.600 | INFO     | 325107949cab4e9fbf33980349010e66 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:05:01.602 | INFO     | 325107949cab4e9fbf33980349010e66 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 34.256ms
2025-08-19 14:05:52.582 | INFO     | d6eee1dac96748c8a43d94080c039a76 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:05:52.584 | INFO     | d6eee1dac96748c8a43d94080c039a76 | 成功认证Java用户: pythontest
2025-08-19 14:05:52.590 | INFO     | d6eee1dac96748c8a43d94080c039a76 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:05:52.591 | INFO     | d6eee1dac96748c8a43d94080c039a76 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:05:52.605 | INFO     | d6eee1dac96748c8a43d94080c039a76 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:05:52.606 | INFO     | d6eee1dac96748c8a43d94080c039a76 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 24.024ms
2025-08-19 14:05:53.129 | INFO     | cf55c65859cb4bb3b97ebda64f5210b5 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:05:53.130 | INFO     | cf55c65859cb4bb3b97ebda64f5210b5 | 成功认证Java用户: pythontest
2025-08-19 14:05:53.136 | INFO     | cf55c65859cb4bb3b97ebda64f5210b5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:05:53.136 | INFO     | cf55c65859cb4bb3b97ebda64f5210b5 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:05:53.150 | INFO     | cf55c65859cb4bb3b97ebda64f5210b5 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:05:53.151 | INFO     | cf55c65859cb4bb3b97ebda64f5210b5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 22.965ms
2025-08-19 14:05:53.374 | INFO     | 333957f59c1c4c9f87057c77eb3a6164 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:05:53.376 | INFO     | 333957f59c1c4c9f87057c77eb3a6164 | 成功认证Java用户: pythontest
2025-08-19 14:05:53.382 | INFO     | 333957f59c1c4c9f87057c77eb3a6164 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:05:53.383 | INFO     | 333957f59c1c4c9f87057c77eb3a6164 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:05:53.398 | INFO     | 333957f59c1c4c9f87057c77eb3a6164 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:05:53.399 | INFO     | 333957f59c1c4c9f87057c77eb3a6164 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 24.598ms
2025-08-19 14:05:53.527 | INFO     | 1b4be7f5ebd74174ae16c6d0edcbff79 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:05:53.529 | INFO     | 1b4be7f5ebd74174ae16c6d0edcbff79 | 成功认证Java用户: pythontest
2025-08-19 14:05:53.534 | INFO     | 1b4be7f5ebd74174ae16c6d0edcbff79 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:05:53.535 | INFO     | 1b4be7f5ebd74174ae16c6d0edcbff79 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:05:53.550 | INFO     | 1b4be7f5ebd74174ae16c6d0edcbff79 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:05:53.551 | INFO     | 1b4be7f5ebd74174ae16c6d0edcbff79 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 22.998ms
2025-08-19 14:05:53.654 | INFO     | d84163a951e14c0783b06a97430d7125 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:05:53.655 | INFO     | d84163a951e14c0783b06a97430d7125 | 成功认证Java用户: pythontest
2025-08-19 14:05:53.660 | INFO     | d84163a951e14c0783b06a97430d7125 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:05:53.661 | INFO     | d84163a951e14c0783b06a97430d7125 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:05:53.675 | INFO     | d84163a951e14c0783b06a97430d7125 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:05:53.677 | INFO     | d84163a951e14c0783b06a97430d7125 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 22.279ms
2025-08-19 14:05:53.835 | INFO     | f82ae49d42b34281a176943145bb24f7 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:05:53.837 | INFO     | f82ae49d42b34281a176943145bb24f7 | 成功认证Java用户: pythontest
2025-08-19 14:05:53.842 | INFO     | f82ae49d42b34281a176943145bb24f7 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:05:53.843 | INFO     | f82ae49d42b34281a176943145bb24f7 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:05:53.858 | INFO     | f82ae49d42b34281a176943145bb24f7 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:05:53.860 | INFO     | f82ae49d42b34281a176943145bb24f7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 23.778ms
2025-08-19 14:05:54.057 | INFO     | 9c76e0dd63834ed39dc274cb7b7c7885 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:05:54.059 | INFO     | 9c76e0dd63834ed39dc274cb7b7c7885 | 成功认证Java用户: pythontest
2025-08-19 14:05:54.065 | INFO     | 9c76e0dd63834ed39dc274cb7b7c7885 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:05:54.065 | INFO     | 9c76e0dd63834ed39dc274cb7b7c7885 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:05:54.079 | INFO     | 9c76e0dd63834ed39dc274cb7b7c7885 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:05:54.081 | INFO     | 9c76e0dd63834ed39dc274cb7b7c7885 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 23.107ms
2025-08-19 14:05:54.610 | INFO     | b7e9d1cc4ba0407485c89b54f25ab52a | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:05:54.611 | INFO     | b7e9d1cc4ba0407485c89b54f25ab52a | 成功认证Java用户: pythontest
2025-08-19 14:05:54.616 | INFO     | b7e9d1cc4ba0407485c89b54f25ab52a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:05:54.617 | INFO     | b7e9d1cc4ba0407485c89b54f25ab52a | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:05:54.632 | INFO     | b7e9d1cc4ba0407485c89b54f25ab52a | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:05:54.634 | INFO     | b7e9d1cc4ba0407485c89b54f25ab52a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 24.084ms
2025-08-19 14:05:54.804 | INFO     | 38efbb1b80dc49999db1a1345d255588 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:05:54.805 | INFO     | 38efbb1b80dc49999db1a1345d255588 | 成功认证Java用户: pythontest
2025-08-19 14:05:54.810 | INFO     | 38efbb1b80dc49999db1a1345d255588 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:05:54.811 | INFO     | 38efbb1b80dc49999db1a1345d255588 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:05:54.824 | INFO     | 38efbb1b80dc49999db1a1345d255588 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:05:54.826 | INFO     | 38efbb1b80dc49999db1a1345d255588 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 22.139ms
2025-08-19 14:05:54.972 | INFO     | b496f8fd417b421799f62199394ac28b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:05:54.973 | INFO     | b496f8fd417b421799f62199394ac28b | 成功认证Java用户: pythontest
2025-08-19 14:05:54.978 | INFO     | b496f8fd417b421799f62199394ac28b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:05:54.979 | INFO     | b496f8fd417b421799f62199394ac28b | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:05:54.995 | INFO     | b496f8fd417b421799f62199394ac28b | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:05:54.996 | INFO     | b496f8fd417b421799f62199394ac28b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 24.003ms
2025-08-19 14:05:55.569 | INFO     | ca4046f262de4243aee7913ef8b66229 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:05:55.570 | INFO     | ca4046f262de4243aee7913ef8b66229 | 成功认证Java用户: pythontest
2025-08-19 14:05:55.578 | INFO     | ca4046f262de4243aee7913ef8b66229 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:05:55.579 | INFO     | ca4046f262de4243aee7913ef8b66229 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:05:55.594 | INFO     | ca4046f262de4243aee7913ef8b66229 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:05:55.596 | INFO     | ca4046f262de4243aee7913ef8b66229 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 26.379ms
2025-08-19 14:05:55.729 | INFO     | 2f55f9f7f4244406a064b40d22c3a760 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:05:55.730 | INFO     | 2f55f9f7f4244406a064b40d22c3a760 | 成功认证Java用户: pythontest
2025-08-19 14:05:55.736 | INFO     | 2f55f9f7f4244406a064b40d22c3a760 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:05:55.737 | INFO     | 2f55f9f7f4244406a064b40d22c3a760 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:05:55.752 | INFO     | 2f55f9f7f4244406a064b40d22c3a760 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:05:55.753 | INFO     | 2f55f9f7f4244406a064b40d22c3a760 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 23.893ms
2025-08-19 14:05:55.906 | INFO     | a2bb28bacf6245d7bb71f5f071e5c076 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:05:55.907 | INFO     | a2bb28bacf6245d7bb71f5f071e5c076 | 成功认证Java用户: pythontest
2025-08-19 14:05:55.912 | INFO     | a2bb28bacf6245d7bb71f5f071e5c076 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:05:55.912 | INFO     | a2bb28bacf6245d7bb71f5f071e5c076 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:05:55.929 | INFO     | a2bb28bacf6245d7bb71f5f071e5c076 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:05:55.930 | INFO     | a2bb28bacf6245d7bb71f5f071e5c076 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 24.855ms
2025-08-19 14:05:56.260 | INFO     | 1598bacaadf24008b12a869b11762f1b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:05:56.262 | INFO     | 1598bacaadf24008b12a869b11762f1b | 成功认证Java用户: pythontest
2025-08-19 14:05:56.269 | INFO     | 1598bacaadf24008b12a869b11762f1b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:05:56.270 | INFO     | 1598bacaadf24008b12a869b11762f1b | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:05:56.285 | INFO     | 1598bacaadf24008b12a869b11762f1b | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:05:56.287 | INFO     | 1598bacaadf24008b12a869b11762f1b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 26.893ms
2025-08-19 14:05:56.689 | INFO     | b362509a261c41999275ef1d4ec3d1e8 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:05:56.692 | INFO     | b362509a261c41999275ef1d4ec3d1e8 | 成功认证Java用户: pythontest
2025-08-19 14:05:56.699 | INFO     | b362509a261c41999275ef1d4ec3d1e8 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:05:56.699 | INFO     | b362509a261c41999275ef1d4ec3d1e8 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:05:56.716 | INFO     | b362509a261c41999275ef1d4ec3d1e8 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:05:56.718 | INFO     | b362509a261c41999275ef1d4ec3d1e8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 28.748ms
2025-08-19 14:05:56.944 | INFO     | 54ac12727e184bb9b4460a32a188448a | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:05:56.945 | INFO     | 54ac12727e184bb9b4460a32a188448a | 成功认证Java用户: pythontest
2025-08-19 14:05:56.952 | INFO     | 54ac12727e184bb9b4460a32a188448a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:05:56.952 | INFO     | 54ac12727e184bb9b4460a32a188448a | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:05:56.968 | INFO     | 54ac12727e184bb9b4460a32a188448a | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:05:56.971 | INFO     | 54ac12727e184bb9b4460a32a188448a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 27.141ms
2025-08-19 14:05:57.323 | INFO     | b34452dfdb1a4276960767c5ecf30158 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:05:57.325 | INFO     | b34452dfdb1a4276960767c5ecf30158 | 成功认证Java用户: pythontest
2025-08-19 14:05:57.335 | INFO     | b34452dfdb1a4276960767c5ecf30158 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:05:57.336 | INFO     | b34452dfdb1a4276960767c5ecf30158 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:05:57.353 | INFO     | b34452dfdb1a4276960767c5ecf30158 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:05:57.356 | INFO     | b34452dfdb1a4276960767c5ecf30158 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 32.723ms
2025-08-19 14:05:57.468 | INFO     | 4547559860a043dc93ef901c38115f6b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:05:57.471 | INFO     | 4547559860a043dc93ef901c38115f6b | 成功认证Java用户: pythontest
2025-08-19 14:05:57.479 | INFO     | 4547559860a043dc93ef901c38115f6b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:05:57.479 | INFO     | 4547559860a043dc93ef901c38115f6b | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:05:57.495 | INFO     | 4547559860a043dc93ef901c38115f6b | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:05:57.497 | INFO     | 4547559860a043dc93ef901c38115f6b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 29.718ms
2025-08-19 14:06:16.189 | INFO     | 4e08baad06c940d8b32bc6622c2451db | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:06:16.190 | INFO     | 4e08baad06c940d8b32bc6622c2451db | 成功认证Java用户: pythontest
2025-08-19 14:06:16.195 | INFO     | 4e08baad06c940d8b32bc6622c2451db | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:06:16.196 | INFO     | 4e08baad06c940d8b32bc6622c2451db | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:06:16.212 | INFO     | 4e08baad06c940d8b32bc6622c2451db | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:06:16.214 | INFO     | 4e08baad06c940d8b32bc6622c2451db | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 25.387ms
2025-08-19 14:06:16.217 | INFO     | 81fc6dbc2dca4816a17166e4ee708c9e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:06:16.219 | INFO     | 81fc6dbc2dca4816a17166e4ee708c9e | 成功认证Java用户: pythontest
2025-08-19 14:06:16.225 | INFO     | 81fc6dbc2dca4816a17166e4ee708c9e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:06:16.226 | INFO     | 81fc6dbc2dca4816a17166e4ee708c9e | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:06:16.241 | INFO     | 81fc6dbc2dca4816a17166e4ee708c9e | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:06:16.242 | INFO     | 81fc6dbc2dca4816a17166e4ee708c9e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 25.267ms
2025-08-19 14:06:16.245 | INFO     | 85ebce5d747a4b1f9b2d31281113dafb | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:06:16.247 | INFO     | 85ebce5d747a4b1f9b2d31281113dafb | 成功认证Java用户: pythontest
2025-08-19 14:06:16.253 | INFO     | 85ebce5d747a4b1f9b2d31281113dafb | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:06:16.254 | INFO     | 85ebce5d747a4b1f9b2d31281113dafb | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:06:16.269 | INFO     | 85ebce5d747a4b1f9b2d31281113dafb | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:06:16.271 | INFO     | 85ebce5d747a4b1f9b2d31281113dafb | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 25.792ms
2025-08-19 14:06:18.398 | INFO     | 61c72ea565124711abd4d29f8eff29f6 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:06:18.399 | INFO     | 8104773eab094e91b904e1a119bae2f1 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:06:18.399 | INFO     | 89b59961df6a45c6a2930e882edb50a7 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:06:18.400 | INFO     | 61c72ea565124711abd4d29f8eff29f6 | 成功认证Java用户: pythontest
2025-08-19 14:06:18.401 | INFO     | 8104773eab094e91b904e1a119bae2f1 | 成功认证Java用户: pythontest
2025-08-19 14:06:18.402 | INFO     | 89b59961df6a45c6a2930e882edb50a7 | 成功认证Java用户: pythontest
2025-08-19 14:06:18.410 | INFO     | 89b59961df6a45c6a2930e882edb50a7 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:06:18.411 | INFO     | 89b59961df6a45c6a2930e882edb50a7 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:06:18.414 | INFO     | 8104773eab094e91b904e1a119bae2f1 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:06:18.415 | INFO     | 8104773eab094e91b904e1a119bae2f1 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:06:18.422 | INFO     | 61c72ea565124711abd4d29f8eff29f6 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:06:18.426 | INFO     | 61c72ea565124711abd4d29f8eff29f6 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 27.614ms
2025-08-19 14:06:18.444 | INFO     | 89b59961df6a45c6a2930e882edb50a7 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:06:18.447 | INFO     | 8104773eab094e91b904e1a119bae2f1 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:06:18.448 | INFO     | 89b59961df6a45c6a2930e882edb50a7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 48.276ms
2025-08-19 14:06:18.450 | INFO     | 8104773eab094e91b904e1a119bae2f1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 51.077ms
2025-08-19 14:06:33.608 | INFO     | cbdf2591e20d482086c4aad3614d13c2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:06:33.610 | INFO     | cbdf2591e20d482086c4aad3614d13c2 | 成功认证Java用户: pythontest
2025-08-19 14:06:33.615 | INFO     | cbdf2591e20d482086c4aad3614d13c2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:06:33.615 | INFO     | cbdf2591e20d482086c4aad3614d13c2 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 14:06:33.631 | INFO     | cbdf2591e20d482086c4aad3614d13c2 | HTTP Request: GET http://*************:6610/api/v1/datasets?id=9e8389ba7cc111f0a6f9ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 14:06:33.631 | INFO     | cbdf2591e20d482086c4aad3614d13c2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/9e8389ba7cc111f0a6f9ea5dc8d5776c | 23.577ms
2025-08-19 14:06:39.462 | INFO     | 39fef03f1a844c559d14626c6fbb42a6 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:06:39.464 | INFO     | 39fef03f1a844c559d14626c6fbb42a6 | 成功认证Java用户: pythontest
2025-08-19 14:06:39.472 | INFO     | 39fef03f1a844c559d14626c6fbb42a6 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:06:39.472 | INFO     | 39fef03f1a844c559d14626c6fbb42a6 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:06:39.489 | INFO     | 39fef03f1a844c559d14626c6fbb42a6 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:06:39.491 | INFO     | 39fef03f1a844c559d14626c6fbb42a6 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 28.801ms
2025-08-19 14:06:39.928 | INFO     | 933d0a9d4c8c4e57a7a3b15115064e48 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:06:39.929 | INFO     | 933d0a9d4c8c4e57a7a3b15115064e48 | 成功认证Java用户: pythontest
2025-08-19 14:06:39.938 | INFO     | 933d0a9d4c8c4e57a7a3b15115064e48 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:06:39.939 | INFO     | 933d0a9d4c8c4e57a7a3b15115064e48 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:06:39.956 | INFO     | 933d0a9d4c8c4e57a7a3b15115064e48 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:06:39.958 | INFO     | 933d0a9d4c8c4e57a7a3b15115064e48 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 29.647ms
2025-08-19 14:06:40.687 | INFO     | f7aff2bef69049daa73a77f79988b544 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:06:40.689 | INFO     | f7aff2bef69049daa73a77f79988b544 | 成功认证Java用户: pythontest
2025-08-19 14:06:40.704 | INFO     | f7aff2bef69049daa73a77f79988b544 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:06:40.706 | INFO     | f7aff2bef69049daa73a77f79988b544 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 18.803ms
2025-08-19 14:07:30.798 | INFO     | 5ddddde314c64b5eadf70d9b99e1164a | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:07:30.800 | INFO     | 5ddddde314c64b5eadf70d9b99e1164a | 成功认证Java用户: pythontest
2025-08-19 14:07:30.814 | INFO     | 5ddddde314c64b5eadf70d9b99e1164a | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:07:30.815 | INFO     | 5ddddde314c64b5eadf70d9b99e1164a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 17.192ms
2025-08-19 14:07:30.820 | INFO     | 70bc842d33bc477eaf19764f46d26e6a | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:07:30.821 | INFO     | 70bc842d33bc477eaf19764f46d26e6a | 成功认证Java用户: pythontest
2025-08-19 14:07:30.826 | INFO     | 70bc842d33bc477eaf19764f46d26e6a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:07:30.827 | INFO     | 70bc842d33bc477eaf19764f46d26e6a | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:07:30.843 | INFO     | 70bc842d33bc477eaf19764f46d26e6a | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:07:30.844 | INFO     | 70bc842d33bc477eaf19764f46d26e6a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 24.620ms
2025-08-19 14:07:30.847 | INFO     | 187e3f7c250f419d8d86bab6c7781a68 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:07:30.848 | INFO     | 187e3f7c250f419d8d86bab6c7781a68 | 成功认证Java用户: pythontest
2025-08-19 14:07:30.854 | INFO     | 187e3f7c250f419d8d86bab6c7781a68 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:07:30.855 | INFO     | 187e3f7c250f419d8d86bab6c7781a68 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:07:30.868 | INFO     | 187e3f7c250f419d8d86bab6c7781a68 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=pagerank&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:07:30.870 | INFO     | 187e3f7c250f419d8d86bab6c7781a68 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=pagerank&desc=true | 22.697ms
2025-08-19 14:07:43.970 | INFO     | 8320458e337e4a02bbfb06c2585ca8c0 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:07:43.972 | INFO     | 8320458e337e4a02bbfb06c2585ca8c0 | 成功认证Java用户: pythontest
2025-08-19 14:07:43.979 | INFO     | 8320458e337e4a02bbfb06c2585ca8c0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:07:43.979 | INFO     | 8320458e337e4a02bbfb06c2585ca8c0 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:07:43.992 | INFO     | 8320458e337e4a02bbfb06c2585ca8c0 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=pagerank&desc=false "HTTP/1.1 200 OK"
2025-08-19 14:07:43.994 | INFO     | 8320458e337e4a02bbfb06c2585ca8c0 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=pagerank&desc=false | 22.920ms
2025-08-19 14:07:44.893 | INFO     | 36462b495063443493d39f5f6d2148bd | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:07:44.894 | INFO     | 36462b495063443493d39f5f6d2148bd | 成功认证Java用户: pythontest
2025-08-19 14:07:44.900 | INFO     | 36462b495063443493d39f5f6d2148bd | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:07:44.901 | INFO     | 36462b495063443493d39f5f6d2148bd | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:07:44.915 | INFO     | 36462b495063443493d39f5f6d2148bd | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=pagerank&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:07:44.917 | INFO     | 36462b495063443493d39f5f6d2148bd | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=pagerank&desc=true | 23.802ms
2025-08-19 14:07:46.461 | INFO     | f9eee78b3a9c4296b8a435d6b84bedce | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:07:46.463 | INFO     | f9eee78b3a9c4296b8a435d6b84bedce | 成功认证Java用户: pythontest
2025-08-19 14:07:46.470 | INFO     | f9eee78b3a9c4296b8a435d6b84bedce | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:07:46.471 | INFO     | f9eee78b3a9c4296b8a435d6b84bedce | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:07:46.491 | INFO     | f9eee78b3a9c4296b8a435d6b84bedce | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:07:46.492 | INFO     | f9eee78b3a9c4296b8a435d6b84bedce | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 31.681ms
2025-08-19 14:07:47.451 | INFO     | 9667f2833f5841b192ce866919fc43d6 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:07:47.453 | INFO     | 9667f2833f5841b192ce866919fc43d6 | 成功认证Java用户: pythontest
2025-08-19 14:07:47.462 | INFO     | 9667f2833f5841b192ce866919fc43d6 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:07:47.463 | INFO     | 9667f2833f5841b192ce866919fc43d6 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:07:47.482 | INFO     | 9667f2833f5841b192ce866919fc43d6 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=update_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:07:47.484 | INFO     | 9667f2833f5841b192ce866919fc43d6 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=update_time&desc=true | 33.111ms
2025-08-19 14:07:48.245 | INFO     | 919169ac1b3440639d2548af75abc062 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:07:48.247 | INFO     | 919169ac1b3440639d2548af75abc062 | 成功认证Java用户: pythontest
2025-08-19 14:07:48.255 | INFO     | 919169ac1b3440639d2548af75abc062 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:07:48.256 | INFO     | 919169ac1b3440639d2548af75abc062 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:07:48.270 | INFO     | 919169ac1b3440639d2548af75abc062 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=pagerank&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:07:48.272 | INFO     | 919169ac1b3440639d2548af75abc062 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=pagerank&desc=true | 27.931ms
2025-08-19 14:07:58.037 | INFO     | 248fc6f43ba44d73906f110e3c860f49 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:07:58.038 | INFO     | 248fc6f43ba44d73906f110e3c860f49 | 成功认证Java用户: pythontest
2025-08-19 14:07:58.039 | INFO     | d76de270609240d6a3f028d8220128b2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:07:58.040 | INFO     | d76de270609240d6a3f028d8220128b2 | 成功认证Java用户: pythontest
2025-08-19 14:07:58.046 | INFO     | 248fc6f43ba44d73906f110e3c860f49 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:07:58.046 | INFO     | 248fc6f43ba44d73906f110e3c860f49 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:07:58.057 | INFO     | d76de270609240d6a3f028d8220128b2 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:07:58.058 | INFO     | 248fc6f43ba44d73906f110e3c860f49 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=pagerank&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:07:58.059 | INFO     | d76de270609240d6a3f028d8220128b2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 20.784ms
2025-08-19 14:07:58.061 | INFO     | 248fc6f43ba44d73906f110e3c860f49 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=pagerank&desc=true | 24.232ms
2025-08-19 14:07:58.062 | INFO     | 45268886d4ad4d8593b1ef7af945d916 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:07:58.063 | INFO     | 45268886d4ad4d8593b1ef7af945d916 | 成功认证Java用户: pythontest
2025-08-19 14:07:58.068 | INFO     | 45268886d4ad4d8593b1ef7af945d916 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:07:58.068 | INFO     | 45268886d4ad4d8593b1ef7af945d916 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:07:58.084 | INFO     | 45268886d4ad4d8593b1ef7af945d916 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:07:58.085 | INFO     | 45268886d4ad4d8593b1ef7af945d916 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 22.626ms
2025-08-19 14:08:50.354 | INFO     | d7129778e7444e6da1762c8d8a73b218 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:08:50.355 | INFO     | f756e16074644d5280d308565316e1e1 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:08:50.356 | INFO     | d7129778e7444e6da1762c8d8a73b218 | 成功认证Java用户: pythontest
2025-08-19 14:08:50.358 | INFO     | f756e16074644d5280d308565316e1e1 | 成功认证Java用户: pythontest
2025-08-19 14:08:50.366 | INFO     | f756e16074644d5280d308565316e1e1 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:08:50.367 | INFO     | f756e16074644d5280d308565316e1e1 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:08:50.373 | INFO     | d7129778e7444e6da1762c8d8a73b218 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:08:50.375 | INFO     | d7129778e7444e6da1762c8d8a73b218 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 20.247ms
2025-08-19 14:08:50.379 | INFO     | e63f1966468f4654a972140e3bbe801e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:08:50.381 | INFO     | e63f1966468f4654a972140e3bbe801e | 成功认证Java用户: pythontest
2025-08-19 14:08:50.382 | INFO     | f756e16074644d5280d308565316e1e1 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=pagerank&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:08:50.384 | INFO     | f756e16074644d5280d308565316e1e1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=pagerank&desc=true | 28.922ms
2025-08-19 14:08:50.390 | INFO     | e63f1966468f4654a972140e3bbe801e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:08:50.391 | INFO     | e63f1966468f4654a972140e3bbe801e | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:08:50.413 | INFO     | e63f1966468f4654a972140e3bbe801e | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:08:50.415 | INFO     | e63f1966468f4654a972140e3bbe801e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 36.074ms
2025-08-19 14:08:53.058 | INFO     | 8030c5b02b1340488a3540b130cad766 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:08:53.060 | INFO     | 8030c5b02b1340488a3540b130cad766 | 成功认证Java用户: pythontest
2025-08-19 14:08:53.064 | INFO     | 8030c5b02b1340488a3540b130cad766 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:08:53.066 | INFO     | 8030c5b02b1340488a3540b130cad766 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:08:53.081 | INFO     | 8030c5b02b1340488a3540b130cad766 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=update_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:08:53.082 | INFO     | 8030c5b02b1340488a3540b130cad766 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=update_time&desc=true | 23.971ms
2025-08-19 14:08:53.953 | INFO     | 3839e8efed0e499f8f35927daa854b64 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:08:53.955 | INFO     | 3839e8efed0e499f8f35927daa854b64 | 成功认证Java用户: pythontest
2025-08-19 14:08:53.961 | INFO     | 3839e8efed0e499f8f35927daa854b64 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:08:53.962 | INFO     | 3839e8efed0e499f8f35927daa854b64 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:08:53.976 | INFO     | 3839e8efed0e499f8f35927daa854b64 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:08:53.977 | INFO     | 3839e8efed0e499f8f35927daa854b64 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 24.704ms
2025-08-19 14:08:54.837 | INFO     | ec94f0314d914024bb7d5ccb39988fdd | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:08:54.839 | INFO     | ec94f0314d914024bb7d5ccb39988fdd | 成功认证Java用户: pythontest
2025-08-19 14:08:54.845 | INFO     | ec94f0314d914024bb7d5ccb39988fdd | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:08:54.846 | INFO     | ec94f0314d914024bb7d5ccb39988fdd | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:08:54.868 | INFO     | ec94f0314d914024bb7d5ccb39988fdd | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=pagerank&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:08:54.870 | INFO     | ec94f0314d914024bb7d5ccb39988fdd | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=pagerank&desc=true | 33.266ms
2025-08-19 14:08:55.908 | INFO     | 60dabe491cd24e4ab64a85ff99a18ac4 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:08:55.910 | INFO     | 60dabe491cd24e4ab64a85ff99a18ac4 | 成功认证Java用户: pythontest
2025-08-19 14:08:55.916 | INFO     | 60dabe491cd24e4ab64a85ff99a18ac4 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:08:55.917 | INFO     | 60dabe491cd24e4ab64a85ff99a18ac4 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:08:55.934 | INFO     | 60dabe491cd24e4ab64a85ff99a18ac4 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=update_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:08:55.936 | INFO     | 60dabe491cd24e4ab64a85ff99a18ac4 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=update_time&desc=true | 28.116ms
2025-08-19 14:08:56.839 | INFO     | d51094f131644cd189272dc27203775f | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:08:56.841 | INFO     | d51094f131644cd189272dc27203775f | 成功认证Java用户: pythontest
2025-08-19 14:08:56.847 | INFO     | d51094f131644cd189272dc27203775f | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:08:56.848 | INFO     | d51094f131644cd189272dc27203775f | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:08:56.863 | INFO     | d51094f131644cd189272dc27203775f | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:08:56.864 | INFO     | d51094f131644cd189272dc27203775f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 24.937ms
2025-08-19 14:08:57.891 | INFO     | 3e6c50bf781b41aea83d0354870dd54f | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:08:57.894 | INFO     | 3e6c50bf781b41aea83d0354870dd54f | 成功认证Java用户: pythontest
2025-08-19 14:08:57.900 | INFO     | 3e6c50bf781b41aea83d0354870dd54f | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:08:57.900 | INFO     | 3e6c50bf781b41aea83d0354870dd54f | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:08:57.911 | INFO     | 3e6c50bf781b41aea83d0354870dd54f | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=pagerank&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:08:57.913 | INFO     | 3e6c50bf781b41aea83d0354870dd54f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=pagerank&desc=true | 21.429ms
2025-08-19 14:09:00.719 | INFO     | 7c03159cfef646cd8d64f1ab9c13c483 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:09:00.721 | INFO     | 7c03159cfef646cd8d64f1ab9c13c483 | 成功认证Java用户: pythontest
2025-08-19 14:09:00.727 | INFO     | 7c03159cfef646cd8d64f1ab9c13c483 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:09:00.728 | INFO     | 7c03159cfef646cd8d64f1ab9c13c483 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:09:00.745 | INFO     | 7c03159cfef646cd8d64f1ab9c13c483 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=update_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:09:00.746 | INFO     | 7c03159cfef646cd8d64f1ab9c13c483 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=update_time&desc=true | 26.226ms
2025-08-19 14:09:01.575 | INFO     | a1510ef4775c4196b04411df00fa8fa7 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:09:01.577 | INFO     | a1510ef4775c4196b04411df00fa8fa7 | 成功认证Java用户: pythontest
2025-08-19 14:09:01.583 | INFO     | a1510ef4775c4196b04411df00fa8fa7 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:09:01.584 | INFO     | a1510ef4775c4196b04411df00fa8fa7 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:09:01.599 | INFO     | a1510ef4775c4196b04411df00fa8fa7 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:09:01.601 | INFO     | a1510ef4775c4196b04411df00fa8fa7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 25.308ms
2025-08-19 14:09:02.742 | INFO     | 79cbf2a8899b4be1af18acaf998ebabc | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:09:02.744 | INFO     | 79cbf2a8899b4be1af18acaf998ebabc | 成功认证Java用户: pythontest
2025-08-19 14:09:02.750 | INFO     | 79cbf2a8899b4be1af18acaf998ebabc | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:09:02.751 | INFO     | 79cbf2a8899b4be1af18acaf998ebabc | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:09:02.761 | INFO     | 79cbf2a8899b4be1af18acaf998ebabc | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=pagerank&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:09:02.762 | INFO     | 79cbf2a8899b4be1af18acaf998ebabc | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=pagerank&desc=true | 20.562ms
2025-08-19 14:10:07.997 | INFO     | 17e248ffd21547b8b6754b6cb59ae27d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:10:07.998 | INFO     | 41a56543fad347cea158bb148a602103 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:10:07.999 | INFO     | da4bf0079aa3466cbb8cf2a53f602c6f | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:10:08.000 | INFO     | 17e248ffd21547b8b6754b6cb59ae27d | 成功认证Java用户: pythontest
2025-08-19 14:10:08.000 | INFO     | 41a56543fad347cea158bb148a602103 | 成功认证Java用户: pythontest
2025-08-19 14:10:08.001 | INFO     | da4bf0079aa3466cbb8cf2a53f602c6f | 成功认证Java用户: pythontest
2025-08-19 14:10:08.008 | INFO     | da4bf0079aa3466cbb8cf2a53f602c6f | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:10:08.009 | INFO     | da4bf0079aa3466cbb8cf2a53f602c6f | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:10:08.011 | INFO     | 41a56543fad347cea158bb148a602103 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:10:08.012 | INFO     | 41a56543fad347cea158bb148a602103 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:10:08.018 | INFO     | 17e248ffd21547b8b6754b6cb59ae27d | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:10:08.019 | INFO     | 17e248ffd21547b8b6754b6cb59ae27d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 22.279ms
2025-08-19 14:10:08.028 | INFO     | 41a56543fad347cea158bb148a602103 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=pagerank&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:10:08.031 | INFO     | da4bf0079aa3466cbb8cf2a53f602c6f | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:10:08.032 | INFO     | 41a56543fad347cea158bb148a602103 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=pagerank&desc=true | 33.785ms
2025-08-19 14:10:08.033 | INFO     | da4bf0079aa3466cbb8cf2a53f602c6f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 34.761ms
2025-08-19 14:10:10.703 | INFO     | 245727d6bad541909ef8de242a18c57a | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:10:10.705 | INFO     | 245727d6bad541909ef8de242a18c57a | 成功认证Java用户: pythontest
2025-08-19 14:10:10.711 | INFO     | 245727d6bad541909ef8de242a18c57a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:10:10.712 | INFO     | 245727d6bad541909ef8de242a18c57a | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:10:10.725 | INFO     | 245727d6bad541909ef8de242a18c57a | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=pagerank&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:10:10.727 | INFO     | 245727d6bad541909ef8de242a18c57a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=pagerank&desc=true | 23.771ms
2025-08-19 14:10:11.161 | INFO     | e02e00a5cbb2481bbd97243a047659d8 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:10:11.163 | INFO     | e02e00a5cbb2481bbd97243a047659d8 | 成功认证Java用户: pythontest
2025-08-19 14:10:11.169 | INFO     | e02e00a5cbb2481bbd97243a047659d8 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:10:11.170 | INFO     | e02e00a5cbb2481bbd97243a047659d8 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:10:11.183 | INFO     | e02e00a5cbb2481bbd97243a047659d8 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=pagerank&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:10:11.185 | INFO     | e02e00a5cbb2481bbd97243a047659d8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=pagerank&desc=true | 24.675ms
2025-08-19 14:10:11.323 | INFO     | 479bf27c384c430d95060c6e78aa7ccd | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:10:11.324 | INFO     | 479bf27c384c430d95060c6e78aa7ccd | 成功认证Java用户: pythontest
2025-08-19 14:10:11.331 | INFO     | 479bf27c384c430d95060c6e78aa7ccd | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:10:11.331 | INFO     | 479bf27c384c430d95060c6e78aa7ccd | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:10:11.345 | INFO     | 479bf27c384c430d95060c6e78aa7ccd | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=pagerank&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:10:11.346 | INFO     | 479bf27c384c430d95060c6e78aa7ccd | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=pagerank&desc=true | 24.310ms
2025-08-19 14:11:15.280 | INFO     | 911654b0e44147dea1b8a2289a5e8767 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:11:15.281 | INFO     | a84863e41d934bcd85de56736ea85f06 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:11:15.281 | INFO     | 46fe4c7a8e9646b599fff11790f0cafa | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:11:15.282 | INFO     | 911654b0e44147dea1b8a2289a5e8767 | 成功认证Java用户: pythontest
2025-08-19 14:11:15.283 | INFO     | a84863e41d934bcd85de56736ea85f06 | 成功认证Java用户: pythontest
2025-08-19 14:11:15.285 | INFO     | 46fe4c7a8e9646b599fff11790f0cafa | 成功认证Java用户: pythontest
2025-08-19 14:11:15.292 | INFO     | 46fe4c7a8e9646b599fff11790f0cafa | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:11:15.292 | INFO     | 46fe4c7a8e9646b599fff11790f0cafa | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:11:15.295 | INFO     | a84863e41d934bcd85de56736ea85f06 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:11:15.295 | INFO     | a84863e41d934bcd85de56736ea85f06 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:11:15.299 | INFO     | 911654b0e44147dea1b8a2289a5e8767 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:11:15.300 | INFO     | 911654b0e44147dea1b8a2289a5e8767 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 20.625ms
2025-08-19 14:11:15.315 | INFO     | 46fe4c7a8e9646b599fff11790f0cafa | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:11:15.317 | INFO     | 46fe4c7a8e9646b599fff11790f0cafa | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 35.840ms
2025-08-19 14:11:15.319 | INFO     | a84863e41d934bcd85de56736ea85f06 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:11:15.319 | INFO     | a84863e41d934bcd85de56736ea85f06 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 38.831ms
2025-08-19 14:11:24.060 | INFO     | 4bf880c6423447428e1c17fa70e3d55d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:11:24.062 | INFO     | 4bf880c6423447428e1c17fa70e3d55d | 成功认证Java用户: pythontest
2025-08-19 14:11:24.068 | INFO     | 4bf880c6423447428e1c17fa70e3d55d | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:11:24.069 | INFO     | 4bf880c6423447428e1c17fa70e3d55d | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:11:24.085 | INFO     | 4bf880c6423447428e1c17fa70e3d55d | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=update_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:11:24.086 | INFO     | 4bf880c6423447428e1c17fa70e3d55d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=update_time&desc=true | 25.219ms
2025-08-19 14:11:25.088 | INFO     | cb7394394af74298afafda89cae94071 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:11:25.089 | INFO     | cb7394394af74298afafda89cae94071 | 成功认证Java用户: pythontest
2025-08-19 14:11:25.095 | INFO     | cb7394394af74298afafda89cae94071 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:11:25.096 | INFO     | cb7394394af74298afafda89cae94071 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:11:25.111 | INFO     | cb7394394af74298afafda89cae94071 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:11:25.112 | INFO     | cb7394394af74298afafda89cae94071 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 24.849ms
2025-08-19 14:12:01.413 | INFO     | 7c4501ba750440c08282a25df7299cce | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:12:01.416 | INFO     | 7c4501ba750440c08282a25df7299cce | 成功认证Java用户: pythontest
2025-08-19 14:12:01.424 | INFO     | 7c4501ba750440c08282a25df7299cce | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:12:01.425 | INFO     | 7c4501ba750440c08282a25df7299cce | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:12:01.444 | INFO     | 7c4501ba750440c08282a25df7299cce | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=false "HTTP/1.1 200 OK"
2025-08-19 14:12:01.445 | INFO     | 7c4501ba750440c08282a25df7299cce | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=false | 31.988ms
2025-08-19 14:12:02.762 | INFO     | a0dea4ca871a493ab73154f4dc9d8e0b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:12:02.764 | INFO     | a0dea4ca871a493ab73154f4dc9d8e0b | 成功认证Java用户: pythontest
2025-08-19 14:12:02.769 | INFO     | a0dea4ca871a493ab73154f4dc9d8e0b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:12:02.770 | INFO     | a0dea4ca871a493ab73154f4dc9d8e0b | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:12:02.786 | INFO     | a0dea4ca871a493ab73154f4dc9d8e0b | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:12:02.788 | INFO     | a0dea4ca871a493ab73154f4dc9d8e0b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 25.252ms
2025-08-19 14:12:03.781 | INFO     | 1351eb49a59c4fbca3d634e62daf43dc | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:12:03.782 | INFO     | 1351eb49a59c4fbca3d634e62daf43dc | 成功认证Java用户: pythontest
2025-08-19 14:12:03.788 | INFO     | 1351eb49a59c4fbca3d634e62daf43dc | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:12:03.790 | INFO     | 1351eb49a59c4fbca3d634e62daf43dc | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:12:03.805 | INFO     | 1351eb49a59c4fbca3d634e62daf43dc | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=update_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:12:03.806 | INFO     | 1351eb49a59c4fbca3d634e62daf43dc | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=update_time&desc=true | 25.739ms
2025-08-19 14:12:04.803 | INFO     | b9ef1577e57240a5871ec26123ccc874 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:12:04.804 | INFO     | b9ef1577e57240a5871ec26123ccc874 | 成功认证Java用户: pythontest
2025-08-19 14:12:04.812 | INFO     | b9ef1577e57240a5871ec26123ccc874 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:12:04.812 | INFO     | b9ef1577e57240a5871ec26123ccc874 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:12:04.827 | INFO     | b9ef1577e57240a5871ec26123ccc874 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=update_time&desc=false "HTTP/1.1 200 OK"
2025-08-19 14:12:04.828 | INFO     | b9ef1577e57240a5871ec26123ccc874 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=update_time&desc=false | 24.713ms
2025-08-19 14:12:05.625 | INFO     | 56d16653a03a43b8b45526f1d0888b71 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:12:05.627 | INFO     | 56d16653a03a43b8b45526f1d0888b71 | 成功认证Java用户: pythontest
2025-08-19 14:12:05.635 | INFO     | 56d16653a03a43b8b45526f1d0888b71 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:12:05.636 | INFO     | 56d16653a03a43b8b45526f1d0888b71 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:12:05.657 | INFO     | 56d16653a03a43b8b45526f1d0888b71 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=update_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:12:05.659 | INFO     | 56d16653a03a43b8b45526f1d0888b71 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=update_time&desc=true | 34.486ms
2025-08-19 14:12:06.657 | INFO     | afd21b86d5944159ae82751499a6f89c | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:12:06.659 | INFO     | afd21b86d5944159ae82751499a6f89c | 成功认证Java用户: pythontest
2025-08-19 14:12:06.665 | INFO     | afd21b86d5944159ae82751499a6f89c | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:12:06.666 | INFO     | afd21b86d5944159ae82751499a6f89c | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:12:06.682 | INFO     | afd21b86d5944159ae82751499a6f89c | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=update_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:12:06.684 | INFO     | afd21b86d5944159ae82751499a6f89c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=update_time&desc=true | 27.136ms
2025-08-19 14:12:35.119 | INFO     | 5b935ab08f3945b9b0678b3722f4a7b6 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:12:35.121 | INFO     | 5b935ab08f3945b9b0678b3722f4a7b6 | 成功认证Java用户: pythontest
2025-08-19 14:12:35.136 | INFO     | 5b935ab08f3945b9b0678b3722f4a7b6 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:12:35.137 | INFO     | 5b935ab08f3945b9b0678b3722f4a7b6 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 17.476ms
2025-08-19 14:12:35.140 | INFO     | d46f6bbccfd246e8a448ef19a3f9b8de | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:12:35.141 | INFO     | d46f6bbccfd246e8a448ef19a3f9b8de | 成功认证Java用户: pythontest
2025-08-19 14:12:35.146 | INFO     | d46f6bbccfd246e8a448ef19a3f9b8de | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:12:35.147 | INFO     | d46f6bbccfd246e8a448ef19a3f9b8de | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:12:35.161 | INFO     | d46f6bbccfd246e8a448ef19a3f9b8de | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:12:35.162 | INFO     | d46f6bbccfd246e8a448ef19a3f9b8de | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 22.544ms
2025-08-19 14:12:35.164 | INFO     | 7797b7dcf22c4456bf6a37a9a0f96a07 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:12:35.165 | INFO     | 7797b7dcf22c4456bf6a37a9a0f96a07 | 成功认证Java用户: pythontest
2025-08-19 14:12:35.169 | INFO     | 7797b7dcf22c4456bf6a37a9a0f96a07 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:12:35.170 | INFO     | 7797b7dcf22c4456bf6a37a9a0f96a07 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:12:35.185 | INFO     | 7797b7dcf22c4456bf6a37a9a0f96a07 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:12:35.186 | INFO     | 7797b7dcf22c4456bf6a37a9a0f96a07 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 22.386ms
2025-08-19 14:12:57.236 | INFO     | a3b2f4040d264485b3a016a73a8cc1e3 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:12:57.237 | INFO     | a3b2f4040d264485b3a016a73a8cc1e3 | 成功认证Java用户: pythontest
2025-08-19 14:12:57.244 | INFO     | a3b2f4040d264485b3a016a73a8cc1e3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:12:57.245 | INFO     | a3b2f4040d264485b3a016a73a8cc1e3 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 14:12:57.262 | INFO     | a3b2f4040d264485b3a016a73a8cc1e3 | HTTP Request: GET http://*************:6610/api/v1/datasets?id=9e8389ba7cc111f0a6f9ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 14:12:57.264 | INFO     | a3b2f4040d264485b3a016a73a8cc1e3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/9e8389ba7cc111f0a6f9ea5dc8d5776c | 28.536ms
2025-08-19 14:13:32.567 | INFO     | a4aed1f1df0343928470d5c149f20116 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:13:32.569 | INFO     | a4aed1f1df0343928470d5c149f20116 | 成功认证Java用户: pythontest
2025-08-19 14:13:32.585 | INFO     | a4aed1f1df0343928470d5c149f20116 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:13:32.586 | INFO     | a4aed1f1df0343928470d5c149f20116 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 18.818ms
2025-08-19 14:13:32.589 | INFO     | 62477b785e9444c8a5895167acc35d45 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:13:32.590 | INFO     | 62477b785e9444c8a5895167acc35d45 | 成功认证Java用户: pythontest
2025-08-19 14:13:32.597 | INFO     | 62477b785e9444c8a5895167acc35d45 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:13:32.598 | INFO     | 62477b785e9444c8a5895167acc35d45 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:13:32.613 | INFO     | 62477b785e9444c8a5895167acc35d45 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:13:32.615 | INFO     | 62477b785e9444c8a5895167acc35d45 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 26.796ms
2025-08-19 14:13:32.618 | INFO     | 19996821c5724ac180aba9ab69f993f8 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:13:32.619 | INFO     | 19996821c5724ac180aba9ab69f993f8 | 成功认证Java用户: pythontest
2025-08-19 14:13:32.627 | INFO     | 19996821c5724ac180aba9ab69f993f8 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:13:32.628 | INFO     | 19996821c5724ac180aba9ab69f993f8 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:13:32.643 | INFO     | 19996821c5724ac180aba9ab69f993f8 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:13:32.645 | INFO     | 19996821c5724ac180aba9ab69f993f8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 26.682ms
2025-08-19 14:13:45.895 | INFO     | 9c3f16a91f0b4578a4013e236038dcc8 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:13:45.896 | INFO     | 8979a287e26347659abaa96676c49107 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:13:45.896 | INFO     | 9c3f16a91f0b4578a4013e236038dcc8 | 成功认证Java用户: pythontest
2025-08-19 14:13:45.900 | INFO     | 8979a287e26347659abaa96676c49107 | 成功认证Java用户: pythontest
2025-08-19 14:13:45.907 | INFO     | 8979a287e26347659abaa96676c49107 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:13:45.907 | INFO     | 8979a287e26347659abaa96676c49107 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:13:45.914 | INFO     | 9c3f16a91f0b4578a4013e236038dcc8 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:13:45.915 | INFO     | 9c3f16a91f0b4578a4013e236038dcc8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 19.477ms
2025-08-19 14:13:45.918 | INFO     | 853f863ef93e4676a10ec34edf19cfbb | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:13:45.919 | INFO     | 853f863ef93e4676a10ec34edf19cfbb | 成功认证Java用户: pythontest
2025-08-19 14:13:45.922 | INFO     | 8979a287e26347659abaa96676c49107 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:13:45.925 | INFO     | 8979a287e26347659abaa96676c49107 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 27.769ms
2025-08-19 14:13:45.926 | INFO     | 853f863ef93e4676a10ec34edf19cfbb | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:13:45.927 | INFO     | 853f863ef93e4676a10ec34edf19cfbb | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:13:45.944 | INFO     | 853f863ef93e4676a10ec34edf19cfbb | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:13:45.945 | INFO     | 853f863ef93e4676a10ec34edf19cfbb | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 27.100ms
2025-08-19 14:14:11.352 | INFO     | 2df691f0333d4c61871a484bbbf18255 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:14:11.353 | INFO     | 2df691f0333d4c61871a484bbbf18255 | 成功认证Java用户: pythontest
2025-08-19 14:14:11.362 | INFO     | 2df691f0333d4c61871a484bbbf18255 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:14:11.363 | INFO     | 2df691f0333d4c61871a484bbbf18255 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:14:11.381 | INFO     | 2df691f0333d4c61871a484bbbf18255 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=false "HTTP/1.1 200 OK"
2025-08-19 14:14:11.383 | INFO     | 2df691f0333d4c61871a484bbbf18255 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=false | 31.920ms
2025-08-19 14:14:12.617 | INFO     | 5380494c94a44b509cb7a1db9fe6fc8f | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:14:12.620 | INFO     | 5380494c94a44b509cb7a1db9fe6fc8f | 成功认证Java用户: pythontest
2025-08-19 14:14:12.627 | INFO     | 5380494c94a44b509cb7a1db9fe6fc8f | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:14:12.628 | INFO     | 5380494c94a44b509cb7a1db9fe6fc8f | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:14:12.647 | INFO     | 5380494c94a44b509cb7a1db9fe6fc8f | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:14:12.649 | INFO     | 5380494c94a44b509cb7a1db9fe6fc8f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 31.215ms
2025-08-19 14:14:13.770 | INFO     | d12b00b58b7d4a05ba5dceba39fe09b8 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:14:13.772 | INFO     | d12b00b58b7d4a05ba5dceba39fe09b8 | 成功认证Java用户: pythontest
2025-08-19 14:14:13.779 | INFO     | d12b00b58b7d4a05ba5dceba39fe09b8 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:14:13.781 | INFO     | d12b00b58b7d4a05ba5dceba39fe09b8 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:14:13.798 | INFO     | d12b00b58b7d4a05ba5dceba39fe09b8 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=false "HTTP/1.1 200 OK"
2025-08-19 14:14:13.801 | INFO     | d12b00b58b7d4a05ba5dceba39fe09b8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=false | 31.033ms
2025-08-19 14:14:15.592 | INFO     | aefe76a9294145b0bde6251aafb856b2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:14:15.595 | INFO     | aefe76a9294145b0bde6251aafb856b2 | 成功认证Java用户: pythontest
2025-08-19 14:14:15.604 | INFO     | aefe76a9294145b0bde6251aafb856b2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:14:15.605 | INFO     | aefe76a9294145b0bde6251aafb856b2 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:14:15.624 | INFO     | aefe76a9294145b0bde6251aafb856b2 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:14:15.627 | INFO     | aefe76a9294145b0bde6251aafb856b2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 34.996ms
2025-08-19 14:14:16.867 | INFO     | f45e9eae5b8e4aa5b7e4ef2077579cd1 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:14:16.869 | INFO     | f45e9eae5b8e4aa5b7e4ef2077579cd1 | 成功认证Java用户: pythontest
2025-08-19 14:14:16.877 | INFO     | f45e9eae5b8e4aa5b7e4ef2077579cd1 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:14:16.878 | INFO     | f45e9eae5b8e4aa5b7e4ef2077579cd1 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:14:16.896 | INFO     | f45e9eae5b8e4aa5b7e4ef2077579cd1 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=false "HTTP/1.1 200 OK"
2025-08-19 14:14:16.899 | INFO     | f45e9eae5b8e4aa5b7e4ef2077579cd1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=false | 31.568ms
2025-08-19 14:14:20.367 | INFO     | 6da17e4dcabf47ce98bfb1f1a3ef4aad | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:14:20.369 | INFO     | 6da17e4dcabf47ce98bfb1f1a3ef4aad | 成功认证Java用户: pythontest
2025-08-19 14:14:20.389 | INFO     | 6da17e4dcabf47ce98bfb1f1a3ef4aad | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:14:20.391 | INFO     | 6da17e4dcabf47ce98bfb1f1a3ef4aad | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 23.788ms
2025-08-19 14:14:20.394 | INFO     | 885c5b7e8f5f4ffebccfb20c1e156762 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:14:20.395 | INFO     | 885c5b7e8f5f4ffebccfb20c1e156762 | 成功认证Java用户: pythontest
2025-08-19 14:14:20.402 | INFO     | 885c5b7e8f5f4ffebccfb20c1e156762 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:14:20.403 | INFO     | 885c5b7e8f5f4ffebccfb20c1e156762 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:14:20.420 | INFO     | 885c5b7e8f5f4ffebccfb20c1e156762 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:14:20.422 | INFO     | 885c5b7e8f5f4ffebccfb20c1e156762 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 29.083ms
2025-08-19 14:14:20.425 | INFO     | fd6fa6715f9449aab77fae7d4b20a4b3 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:14:20.428 | INFO     | fd6fa6715f9449aab77fae7d4b20a4b3 | 成功认证Java用户: pythontest
2025-08-19 14:14:20.434 | INFO     | fd6fa6715f9449aab77fae7d4b20a4b3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:14:20.436 | INFO     | fd6fa6715f9449aab77fae7d4b20a4b3 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:14:20.455 | INFO     | fd6fa6715f9449aab77fae7d4b20a4b3 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:14:20.458 | INFO     | fd6fa6715f9449aab77fae7d4b20a4b3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 32.065ms
2025-08-19 14:14:21.064 | INFO     | bb1d3a1edc534a08ae0434e5726c8b2f | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:14:21.066 | INFO     | 0fe8adb406814999974c32590da40728 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:14:21.067 | INFO     | 2673d7c8b12e49aea3132de9faec66c9 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:14:21.068 | INFO     | bb1d3a1edc534a08ae0434e5726c8b2f | 成功认证Java用户: pythontest
2025-08-19 14:14:21.069 | INFO     | 0fe8adb406814999974c32590da40728 | 成功认证Java用户: pythontest
2025-08-19 14:14:21.070 | INFO     | 2673d7c8b12e49aea3132de9faec66c9 | 成功认证Java用户: pythontest
2025-08-19 14:14:21.082 | INFO     | 2673d7c8b12e49aea3132de9faec66c9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:14:21.084 | INFO     | 2673d7c8b12e49aea3132de9faec66c9 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:14:21.089 | INFO     | 0fe8adb406814999974c32590da40728 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:14:21.090 | INFO     | 0fe8adb406814999974c32590da40728 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:14:21.095 | INFO     | bb1d3a1edc534a08ae0434e5726c8b2f | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:14:21.100 | INFO     | bb1d3a1edc534a08ae0434e5726c8b2f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 35.318ms
2025-08-19 14:14:21.114 | INFO     | 2673d7c8b12e49aea3132de9faec66c9 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:14:21.117 | INFO     | 0fe8adb406814999974c32590da40728 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:14:21.119 | INFO     | 2673d7c8b12e49aea3132de9faec66c9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 51.836ms
2025-08-19 14:14:21.120 | INFO     | 0fe8adb406814999974c32590da40728 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 54.707ms
2025-08-19 14:14:22.973 | INFO     | 08e0fad411ca4c3c8b24fa4af0da990a | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:14:22.975 | INFO     | 08e0fad411ca4c3c8b24fa4af0da990a | 成功认证Java用户: pythontest
2025-08-19 14:14:22.981 | INFO     | 08e0fad411ca4c3c8b24fa4af0da990a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:14:22.982 | INFO     | 08e0fad411ca4c3c8b24fa4af0da990a | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:14:22.998 | INFO     | 08e0fad411ca4c3c8b24fa4af0da990a | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:14:23.000 | INFO     | 08e0fad411ca4c3c8b24fa4af0da990a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 27.243ms
2025-08-19 14:14:35.487 | INFO     | fed27e9cb34a43ba9c373a2df412f1b7 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:14:35.488 | INFO     | 8714701c086a470a8946089f8319b3dc | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:14:35.489 | INFO     | f9a699cd302742a695ae8992b15641da | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:14:35.489 | INFO     | fed27e9cb34a43ba9c373a2df412f1b7 | 成功认证Java用户: pythontest
2025-08-19 14:14:35.490 | INFO     | 8714701c086a470a8946089f8319b3dc | 成功认证Java用户: pythontest
2025-08-19 14:14:35.491 | INFO     | f9a699cd302742a695ae8992b15641da | 成功认证Java用户: pythontest
2025-08-19 14:14:35.499 | INFO     | f9a699cd302742a695ae8992b15641da | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:14:35.499 | INFO     | f9a699cd302742a695ae8992b15641da | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:14:35.502 | INFO     | 8714701c086a470a8946089f8319b3dc | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:14:35.503 | INFO     | 8714701c086a470a8946089f8319b3dc | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:14:35.507 | INFO     | fed27e9cb34a43ba9c373a2df412f1b7 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:14:35.509 | INFO     | fed27e9cb34a43ba9c373a2df412f1b7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 20.696ms
2025-08-19 14:14:35.521 | INFO     | f9a699cd302742a695ae8992b15641da | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:14:35.521 | INFO     | 8714701c086a470a8946089f8319b3dc | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:14:35.523 | INFO     | f9a699cd302742a695ae8992b15641da | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 33.948ms
2025-08-19 14:14:35.523 | INFO     | 8714701c086a470a8946089f8319b3dc | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 35.015ms
2025-08-19 14:16:57.523 | INFO     | 1a57774e04294338a95f5cff5124dc54 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:16:57.525 | INFO     | 1a57774e04294338a95f5cff5124dc54 | 成功认证Java用户: pythontest
2025-08-19 14:16:57.531 | INFO     | 1a57774e04294338a95f5cff5124dc54 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:16:57.532 | INFO     | 1a57774e04294338a95f5cff5124dc54 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:16:57.549 | INFO     | 1a57774e04294338a95f5cff5124dc54 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=false "HTTP/1.1 200 OK"
2025-08-19 14:16:57.550 | INFO     | 1a57774e04294338a95f5cff5124dc54 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=false | 27.119ms
2025-08-19 14:16:58.686 | INFO     | 1e1c3aeb17f24d4dbbdf02091042a2f3 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:16:58.687 | INFO     | 1e1c3aeb17f24d4dbbdf02091042a2f3 | 成功认证Java用户: pythontest
2025-08-19 14:16:58.696 | INFO     | 1e1c3aeb17f24d4dbbdf02091042a2f3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:16:58.697 | INFO     | 1e1c3aeb17f24d4dbbdf02091042a2f3 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:16:58.715 | INFO     | 1e1c3aeb17f24d4dbbdf02091042a2f3 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:16:58.716 | INFO     | 1e1c3aeb17f24d4dbbdf02091042a2f3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 29.653ms
2025-08-19 14:16:59.848 | INFO     | 1feb43f5bf34430da8ff61013e982e46 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:16:59.850 | INFO     | 1feb43f5bf34430da8ff61013e982e46 | 成功认证Java用户: pythontest
2025-08-19 14:16:59.858 | INFO     | 1feb43f5bf34430da8ff61013e982e46 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:16:59.859 | INFO     | 1feb43f5bf34430da8ff61013e982e46 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:16:59.875 | INFO     | 1feb43f5bf34430da8ff61013e982e46 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=false "HTTP/1.1 200 OK"
2025-08-19 14:16:59.877 | INFO     | 1feb43f5bf34430da8ff61013e982e46 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=false | 29.787ms
2025-08-19 14:17:00.942 | INFO     | e75f9cfb8a504f5a8c5106a3a9ac9247 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:00.944 | INFO     | e75f9cfb8a504f5a8c5106a3a9ac9247 | 成功认证Java用户: pythontest
2025-08-19 14:17:00.953 | INFO     | e75f9cfb8a504f5a8c5106a3a9ac9247 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:00.955 | INFO     | e75f9cfb8a504f5a8c5106a3a9ac9247 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:17:00.975 | INFO     | e75f9cfb8a504f5a8c5106a3a9ac9247 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:17:00.977 | INFO     | e75f9cfb8a504f5a8c5106a3a9ac9247 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 35.198ms
2025-08-19 14:17:01.853 | INFO     | 9ba47dbce7f54ce29d5b08da3bb92c0c | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:01.856 | INFO     | 9ba47dbce7f54ce29d5b08da3bb92c0c | 成功认证Java用户: pythontest
2025-08-19 14:17:01.865 | INFO     | 9ba47dbce7f54ce29d5b08da3bb92c0c | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:01.867 | INFO     | 9ba47dbce7f54ce29d5b08da3bb92c0c | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:17:01.885 | INFO     | 9ba47dbce7f54ce29d5b08da3bb92c0c | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=false "HTTP/1.1 200 OK"
2025-08-19 14:17:01.887 | INFO     | 9ba47dbce7f54ce29d5b08da3bb92c0c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=false | 34.310ms
2025-08-19 14:17:04.227 | INFO     | 2db022b6003342f4929e765754897d17 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:04.229 | INFO     | 2db022b6003342f4929e765754897d17 | 成功认证Java用户: pythontest
2025-08-19 14:17:04.236 | INFO     | 2db022b6003342f4929e765754897d17 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:04.237 | INFO     | 2db022b6003342f4929e765754897d17 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:17:04.254 | INFO     | 2db022b6003342f4929e765754897d17 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:17:04.256 | INFO     | 2db022b6003342f4929e765754897d17 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 29.018ms
2025-08-19 14:17:05.841 | INFO     | 7a4127524fba4894a7b2d369ee19f282 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:05.843 | INFO     | 7a4127524fba4894a7b2d369ee19f282 | 成功认证Java用户: pythontest
2025-08-19 14:17:05.850 | INFO     | 7a4127524fba4894a7b2d369ee19f282 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:05.851 | INFO     | 7a4127524fba4894a7b2d369ee19f282 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:17:05.868 | INFO     | 7a4127524fba4894a7b2d369ee19f282 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:17:05.869 | INFO     | 7a4127524fba4894a7b2d369ee19f282 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 28.248ms
2025-08-19 14:17:08.925 | INFO     | 008d554309c843509f444df9ae90704f | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:08.927 | INFO     | 008d554309c843509f444df9ae90704f | 成功认证Java用户: pythontest
2025-08-19 14:17:08.934 | INFO     | 008d554309c843509f444df9ae90704f | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:08.935 | INFO     | 008d554309c843509f444df9ae90704f | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:17:08.959 | INFO     | 008d554309c843509f444df9ae90704f | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=false "HTTP/1.1 200 OK"
2025-08-19 14:17:08.961 | INFO     | 008d554309c843509f444df9ae90704f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=false | 36.392ms
2025-08-19 14:17:09.870 | INFO     | df415bad2630427db2acf1f529988ef0 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:09.872 | INFO     | df415bad2630427db2acf1f529988ef0 | 成功认证Java用户: pythontest
2025-08-19 14:17:09.880 | INFO     | df415bad2630427db2acf1f529988ef0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:09.881 | INFO     | df415bad2630427db2acf1f529988ef0 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:17:09.898 | INFO     | df415bad2630427db2acf1f529988ef0 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:17:09.900 | INFO     | df415bad2630427db2acf1f529988ef0 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 30.467ms
2025-08-19 14:17:15.093 | INFO     | 41af005fe5cd4e9a99e9abf074c9f228 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:15.095 | INFO     | 41af005fe5cd4e9a99e9abf074c9f228 | 成功认证Java用户: pythontest
2025-08-19 14:17:15.103 | INFO     | 41af005fe5cd4e9a99e9abf074c9f228 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:15.104 | INFO     | 41af005fe5cd4e9a99e9abf074c9f228 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-19 14:17:15.226 | INFO     | 41af005fe5cd4e9a99e9abf074c9f228 | HTTP Request: PUT http://*************:6610/api/v1/datasets/721d81667cbf11f09259ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 14:17:15.228 | INFO     | 41af005fe5cd4e9a99e9abf074c9f228 | 127.0.0.1       | PUT      | 200    | /api/iot/v1/knowledge-base/721d81667cbf11f09259ea5dc8d5776c | 134.970ms
2025-08-19 14:17:15.241 | INFO     | a1d00a66b5bb49489a3fdd091a19eafb | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:15.244 | INFO     | a1d00a66b5bb49489a3fdd091a19eafb | 成功认证Java用户: pythontest
2025-08-19 14:17:15.253 | INFO     | a1d00a66b5bb49489a3fdd091a19eafb | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:15.254 | INFO     | a1d00a66b5bb49489a3fdd091a19eafb | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:17:15.271 | INFO     | a1d00a66b5bb49489a3fdd091a19eafb | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:17:15.273 | INFO     | a1d00a66b5bb49489a3fdd091a19eafb | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 31.616ms
2025-08-19 14:17:15.276 | INFO     | ee0df93d5cca475e94d678bbf55d2532 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:15.277 | INFO     | ee0df93d5cca475e94d678bbf55d2532 | 成功认证Java用户: pythontest
2025-08-19 14:17:15.284 | INFO     | ee0df93d5cca475e94d678bbf55d2532 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:15.285 | INFO     | ee0df93d5cca475e94d678bbf55d2532 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:17:15.305 | INFO     | ee0df93d5cca475e94d678bbf55d2532 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:17:15.307 | INFO     | ee0df93d5cca475e94d678bbf55d2532 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 31.699ms
2025-08-19 14:17:18.953 | INFO     | 0280284e33a44c22913c15f48b727a0b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:18.956 | INFO     | 0280284e33a44c22913c15f48b727a0b | 成功认证Java用户: pythontest
2025-08-19 14:17:18.963 | INFO     | 0280284e33a44c22913c15f48b727a0b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:18.964 | INFO     | 0280284e33a44c22913c15f48b727a0b | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:17:18.980 | INFO     | 0280284e33a44c22913c15f48b727a0b | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=false "HTTP/1.1 200 OK"
2025-08-19 14:17:18.982 | INFO     | 0280284e33a44c22913c15f48b727a0b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=false | 28.846ms
2025-08-19 14:17:20.205 | INFO     | ebccd76a9c9041e985ed229146484bf1 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:20.207 | INFO     | ebccd76a9c9041e985ed229146484bf1 | 成功认证Java用户: pythontest
2025-08-19 14:17:20.214 | INFO     | ebccd76a9c9041e985ed229146484bf1 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:20.215 | INFO     | ebccd76a9c9041e985ed229146484bf1 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:17:20.232 | INFO     | ebccd76a9c9041e985ed229146484bf1 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:17:20.234 | INFO     | ebccd76a9c9041e985ed229146484bf1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 28.779ms
2025-08-19 14:17:21.117 | INFO     | 22a3b7d8d1f24274a9d49758e50b0e73 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:21.119 | INFO     | 22a3b7d8d1f24274a9d49758e50b0e73 | 成功认证Java用户: pythontest
2025-08-19 14:17:21.127 | INFO     | 22a3b7d8d1f24274a9d49758e50b0e73 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:21.128 | INFO     | 22a3b7d8d1f24274a9d49758e50b0e73 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:17:21.144 | INFO     | 22a3b7d8d1f24274a9d49758e50b0e73 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=false "HTTP/1.1 200 OK"
2025-08-19 14:17:21.146 | INFO     | 22a3b7d8d1f24274a9d49758e50b0e73 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=false | 28.897ms
2025-08-19 14:17:22.402 | INFO     | 3b6e6f40c615472784274bdeb69f1ccd | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:22.403 | INFO     | 3b6e6f40c615472784274bdeb69f1ccd | 成功认证Java用户: pythontest
2025-08-19 14:17:22.410 | INFO     | 3b6e6f40c615472784274bdeb69f1ccd | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:22.411 | INFO     | 3b6e6f40c615472784274bdeb69f1ccd | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:17:22.429 | INFO     | 3b6e6f40c615472784274bdeb69f1ccd | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:17:22.431 | INFO     | 3b6e6f40c615472784274bdeb69f1ccd | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 29.139ms
2025-08-19 14:17:37.335 | INFO     | 51cbe3027a744e879d8bc97c941a5657 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:37.337 | INFO     | 51cbe3027a744e879d8bc97c941a5657 | 成功认证Java用户: pythontest
2025-08-19 14:17:37.344 | INFO     | 51cbe3027a744e879d8bc97c941a5657 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:37.345 | INFO     | 51cbe3027a744e879d8bc97c941a5657 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:17:37.362 | INFO     | 51cbe3027a744e879d8bc97c941a5657 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:17:37.364 | INFO     | 51cbe3027a744e879d8bc97c941a5657 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 28.906ms
2025-08-19 14:17:37.828 | INFO     | 4cbfddd2c97d4e78b78c562a21aab2a9 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:37.830 | INFO     | 4cbfddd2c97d4e78b78c562a21aab2a9 | 成功认证Java用户: pythontest
2025-08-19 14:17:37.838 | INFO     | 4cbfddd2c97d4e78b78c562a21aab2a9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:37.839 | INFO     | 4cbfddd2c97d4e78b78c562a21aab2a9 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:17:37.855 | INFO     | 4cbfddd2c97d4e78b78c562a21aab2a9 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:17:37.857 | INFO     | 4cbfddd2c97d4e78b78c562a21aab2a9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 28.824ms
2025-08-19 14:17:39.876 | INFO     | e99b2de4d89140d0a020da627959b46b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:39.878 | INFO     | e99b2de4d89140d0a020da627959b46b | 成功认证Java用户: pythontest
2025-08-19 14:17:39.897 | INFO     | e99b2de4d89140d0a020da627959b46b | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:17:39.899 | INFO     | e99b2de4d89140d0a020da627959b46b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 22.399ms
2025-08-19 14:17:39.902 | INFO     | 6b1e0a6f62714e67be2af8898535f577 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:39.904 | INFO     | 6b1e0a6f62714e67be2af8898535f577 | 成功认证Java用户: pythontest
2025-08-19 14:17:39.912 | INFO     | 6b1e0a6f62714e67be2af8898535f577 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:39.913 | INFO     | 6b1e0a6f62714e67be2af8898535f577 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:17:39.929 | INFO     | 6b1e0a6f62714e67be2af8898535f577 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:17:39.931 | INFO     | 6b1e0a6f62714e67be2af8898535f577 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 28.773ms
2025-08-19 14:17:39.933 | INFO     | f69677d552af43cb9797385407ce1f26 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:39.935 | INFO     | f69677d552af43cb9797385407ce1f26 | 成功认证Java用户: pythontest
2025-08-19 14:17:39.941 | INFO     | f69677d552af43cb9797385407ce1f26 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:39.943 | INFO     | f69677d552af43cb9797385407ce1f26 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:17:39.959 | INFO     | f69677d552af43cb9797385407ce1f26 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:17:39.961 | INFO     | f69677d552af43cb9797385407ce1f26 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 27.598ms
2025-08-19 14:17:43.869 | INFO     | 74215bc37eba47018f99246429d46b02 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:43.871 | INFO     | 74215bc37eba47018f99246429d46b02 | 成功认证Java用户: pythontest
2025-08-19 14:17:43.879 | INFO     | 74215bc37eba47018f99246429d46b02 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:43.880 | INFO     | 74215bc37eba47018f99246429d46b02 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:17:43.898 | INFO     | 74215bc37eba47018f99246429d46b02 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=false "HTTP/1.1 200 OK"
2025-08-19 14:17:43.900 | INFO     | 74215bc37eba47018f99246429d46b02 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=false | 31.245ms
2025-08-19 14:17:45.399 | INFO     | c78cb906681e448b80e198155aae287f | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:45.402 | INFO     | c78cb906681e448b80e198155aae287f | 成功认证Java用户: pythontest
2025-08-19 14:17:45.410 | INFO     | c78cb906681e448b80e198155aae287f | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:45.411 | INFO     | c78cb906681e448b80e198155aae287f | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:17:45.427 | INFO     | c78cb906681e448b80e198155aae287f | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:17:45.428 | INFO     | c78cb906681e448b80e198155aae287f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 29.279ms
2025-08-19 14:17:46.435 | INFO     | 8c49394ae74f4f6e8e394bd95af4a018 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:46.436 | INFO     | 8c49394ae74f4f6e8e394bd95af4a018 | 成功认证Java用户: pythontest
2025-08-19 14:17:46.444 | INFO     | 8c49394ae74f4f6e8e394bd95af4a018 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:46.444 | INFO     | 8c49394ae74f4f6e8e394bd95af4a018 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:17:46.462 | INFO     | 8c49394ae74f4f6e8e394bd95af4a018 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=false "HTTP/1.1 200 OK"
2025-08-19 14:17:46.464 | INFO     | 8c49394ae74f4f6e8e394bd95af4a018 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=false | 29.616ms
2025-08-19 14:17:47.782 | INFO     | f579d4c1b9654aceaa2e9f9cb55dcc17 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:47.783 | INFO     | f579d4c1b9654aceaa2e9f9cb55dcc17 | 成功认证Java用户: pythontest
2025-08-19 14:17:47.791 | INFO     | f579d4c1b9654aceaa2e9f9cb55dcc17 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:47.792 | INFO     | f579d4c1b9654aceaa2e9f9cb55dcc17 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:17:47.807 | INFO     | f579d4c1b9654aceaa2e9f9cb55dcc17 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:17:47.810 | INFO     | f579d4c1b9654aceaa2e9f9cb55dcc17 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 27.879ms
2025-08-19 14:17:48.781 | INFO     | 70717de7857847c484a5c130e972c847 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:48.785 | INFO     | 70717de7857847c484a5c130e972c847 | 成功认证Java用户: pythontest
2025-08-19 14:17:48.792 | INFO     | 70717de7857847c484a5c130e972c847 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:48.793 | INFO     | 70717de7857847c484a5c130e972c847 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:17:48.810 | INFO     | 70717de7857847c484a5c130e972c847 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=false "HTTP/1.1 200 OK"
2025-08-19 14:17:48.812 | INFO     | 70717de7857847c484a5c130e972c847 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=false | 30.843ms
2025-08-19 14:17:52.416 | INFO     | 9f2edf05cae149b9bc7837d207e2cfc2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:52.418 | INFO     | 9f2edf05cae149b9bc7837d207e2cfc2 | 成功认证Java用户: pythontest
2025-08-19 14:17:52.427 | INFO     | 9f2edf05cae149b9bc7837d207e2cfc2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:52.428 | INFO     | 9f2edf05cae149b9bc7837d207e2cfc2 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:17:52.444 | INFO     | 9f2edf05cae149b9bc7837d207e2cfc2 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:17:52.447 | INFO     | 9f2edf05cae149b9bc7837d207e2cfc2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 31.502ms
2025-08-19 14:17:53.280 | INFO     | 06420e4e51f64e89b1c1dfa7a25355b0 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:53.282 | INFO     | 06420e4e51f64e89b1c1dfa7a25355b0 | 成功认证Java用户: pythontest
2025-08-19 14:17:53.289 | INFO     | 06420e4e51f64e89b1c1dfa7a25355b0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:53.290 | INFO     | 06420e4e51f64e89b1c1dfa7a25355b0 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:17:53.308 | INFO     | 06420e4e51f64e89b1c1dfa7a25355b0 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=false "HTTP/1.1 200 OK"
2025-08-19 14:17:53.310 | INFO     | 06420e4e51f64e89b1c1dfa7a25355b0 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=false | 31.165ms
2025-08-19 14:17:54.267 | INFO     | 868e5d23750147dca711ff199900e691 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:17:54.269 | INFO     | 868e5d23750147dca711ff199900e691 | 成功认证Java用户: pythontest
2025-08-19 14:17:54.278 | INFO     | 868e5d23750147dca711ff199900e691 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:17:54.279 | INFO     | 868e5d23750147dca711ff199900e691 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:17:54.299 | INFO     | 868e5d23750147dca711ff199900e691 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:17:54.301 | INFO     | 868e5d23750147dca711ff199900e691 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 34.699ms
2025-08-19 14:19:18.407 | INFO     | 2772068e6a28484f9d39b4953f73d767 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:19:18.421 | INFO     | 2772068e6a28484f9d39b4953f73d767 | 成功认证Java用户: pythontest
2025-08-19 14:19:18.422 | INFO     | 9a4eb2467b6a4c22b6384a7d53bd8da2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:19:18.423 | INFO     | 9a4eb2467b6a4c22b6384a7d53bd8da2 | 成功认证Java用户: pythontest
2025-08-19 14:19:18.429 | INFO     | 2772068e6a28484f9d39b4953f73d767 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:19:18.429 | INFO     | 2772068e6a28484f9d39b4953f73d767 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:19:18.439 | INFO     | 9a4eb2467b6a4c22b6384a7d53bd8da2 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:19:18.440 | INFO     | 9a4eb2467b6a4c22b6384a7d53bd8da2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 18.235ms
2025-08-19 14:19:18.441 | INFO     | a3ce7638f5904a7f97851bb286018035 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:19:18.442 | INFO     | a3ce7638f5904a7f97851bb286018035 | 成功认证Java用户: pythontest
2025-08-19 14:19:18.444 | INFO     | 2772068e6a28484f9d39b4953f73d767 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:19:18.446 | INFO     | 2772068e6a28484f9d39b4953f73d767 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 38.765ms
2025-08-19 14:19:18.449 | INFO     | a3ce7638f5904a7f97851bb286018035 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:19:18.450 | INFO     | a3ce7638f5904a7f97851bb286018035 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:19:18.462 | INFO     | a3ce7638f5904a7f97851bb286018035 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:19:18.463 | INFO     | a3ce7638f5904a7f97851bb286018035 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 22.373ms
2025-08-19 14:19:42.731 | INFO     | 1e9ad5c85c7b4049bdeb177c5ff9d647 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:19:42.732 | INFO     | d1873b4639fa4c04a176373655e47e99 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:19:42.733 | INFO     | cbcfdb6e49484ed192d3ea3ee634fde3 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:19:42.733 | INFO     | 1e9ad5c85c7b4049bdeb177c5ff9d647 | 成功认证Java用户: pythontest
2025-08-19 14:19:42.734 | INFO     | d1873b4639fa4c04a176373655e47e99 | 成功认证Java用户: pythontest
2025-08-19 14:19:42.735 | INFO     | cbcfdb6e49484ed192d3ea3ee634fde3 | 成功认证Java用户: pythontest
2025-08-19 14:19:42.743 | INFO     | cbcfdb6e49484ed192d3ea3ee634fde3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:19:42.743 | INFO     | cbcfdb6e49484ed192d3ea3ee634fde3 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:19:42.747 | INFO     | d1873b4639fa4c04a176373655e47e99 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:19:42.748 | INFO     | d1873b4639fa4c04a176373655e47e99 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:19:42.751 | INFO     | 1e9ad5c85c7b4049bdeb177c5ff9d647 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:19:42.753 | INFO     | 1e9ad5c85c7b4049bdeb177c5ff9d647 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 21.742ms
2025-08-19 14:19:42.770 | INFO     | cbcfdb6e49484ed192d3ea3ee634fde3 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:19:42.771 | INFO     | d1873b4639fa4c04a176373655e47e99 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:19:42.771 | INFO     | cbcfdb6e49484ed192d3ea3ee634fde3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 37.940ms
2025-08-19 14:19:42.773 | INFO     | d1873b4639fa4c04a176373655e47e99 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 40.915ms
2025-08-19 14:19:55.033 | INFO     | 3a83b97b6fe84557acf079484dc4dbdb | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:19:55.034 | INFO     | 8fa2e2ae77854e80a29b78272357bc96 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:19:55.034 | INFO     | 1cb18e87601641fda101c27599b3d01d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:19:55.035 | INFO     | 3a83b97b6fe84557acf079484dc4dbdb | 成功认证Java用户: pythontest
2025-08-19 14:19:55.035 | INFO     | 8fa2e2ae77854e80a29b78272357bc96 | 成功认证Java用户: pythontest
2025-08-19 14:19:55.036 | INFO     | 1cb18e87601641fda101c27599b3d01d | 成功认证Java用户: pythontest
2025-08-19 14:19:55.043 | INFO     | 1cb18e87601641fda101c27599b3d01d | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:19:55.043 | INFO     | 1cb18e87601641fda101c27599b3d01d | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:19:55.046 | INFO     | 8fa2e2ae77854e80a29b78272357bc96 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:19:55.047 | INFO     | 8fa2e2ae77854e80a29b78272357bc96 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:19:55.052 | INFO     | 3a83b97b6fe84557acf079484dc4dbdb | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:19:55.052 | INFO     | 3a83b97b6fe84557acf079484dc4dbdb | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 19.685ms
2025-08-19 14:19:55.065 | INFO     | 1cb18e87601641fda101c27599b3d01d | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:19:55.067 | INFO     | 8fa2e2ae77854e80a29b78272357bc96 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:19:55.068 | INFO     | 1cb18e87601641fda101c27599b3d01d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 33.551ms
2025-08-19 14:19:55.068 | INFO     | 8fa2e2ae77854e80a29b78272357bc96 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 35.888ms
2025-08-19 14:21:14.378 | INFO     | 7b57d93f045a4b629c8d5a44d7330c85 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:21:14.379 | INFO     | 1dc06766b7304bb7aacdce9f63fcdb81 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:21:14.380 | INFO     | 4a95fe8ae6dd44c1b9a7e0918b0fe294 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:21:14.381 | INFO     | 7b57d93f045a4b629c8d5a44d7330c85 | 成功认证Java用户: pythontest
2025-08-19 14:21:14.382 | INFO     | 1dc06766b7304bb7aacdce9f63fcdb81 | 成功认证Java用户: pythontest
2025-08-19 14:21:14.384 | INFO     | 4a95fe8ae6dd44c1b9a7e0918b0fe294 | 成功认证Java用户: pythontest
2025-08-19 14:21:14.392 | INFO     | 1dc06766b7304bb7aacdce9f63fcdb81 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:21:14.393 | INFO     | 1dc06766b7304bb7aacdce9f63fcdb81 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:21:14.396 | INFO     | 4a95fe8ae6dd44c1b9a7e0918b0fe294 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:21:14.397 | INFO     | 4a95fe8ae6dd44c1b9a7e0918b0fe294 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:21:14.399 | INFO     | 7b57d93f045a4b629c8d5a44d7330c85 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:21:14.402 | INFO     | 7b57d93f045a4b629c8d5a44d7330c85 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 24.581ms
2025-08-19 14:21:14.418 | INFO     | 1dc06766b7304bb7aacdce9f63fcdb81 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:21:14.418 | INFO     | 4a95fe8ae6dd44c1b9a7e0918b0fe294 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:21:14.420 | INFO     | 1dc06766b7304bb7aacdce9f63fcdb81 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 40.979ms
2025-08-19 14:21:14.421 | INFO     | 4a95fe8ae6dd44c1b9a7e0918b0fe294 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 41.019ms
2025-08-19 14:21:19.507 | INFO     | 4eea66790970436680b978ae82a4850d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:21:19.508 | INFO     | 4eea66790970436680b978ae82a4850d | 成功认证Java用户: pythontest
2025-08-19 14:21:19.526 | INFO     | 4eea66790970436680b978ae82a4850d | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:21:19.528 | INFO     | 4eea66790970436680b978ae82a4850d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 20.464ms
2025-08-19 14:21:19.533 | INFO     | 6001adbd9f514c649199d59c4d09dcb3 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:21:19.535 | INFO     | 6001adbd9f514c649199d59c4d09dcb3 | 成功认证Java用户: pythontest
2025-08-19 14:21:19.542 | INFO     | 6001adbd9f514c649199d59c4d09dcb3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:21:19.543 | INFO     | 6001adbd9f514c649199d59c4d09dcb3 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:21:19.562 | INFO     | 6001adbd9f514c649199d59c4d09dcb3 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:21:19.564 | INFO     | 6001adbd9f514c649199d59c4d09dcb3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 30.941ms
2025-08-19 14:21:19.567 | INFO     | 16a998dc866e4672ad77716397f42d2c | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:21:19.569 | INFO     | 16a998dc866e4672ad77716397f42d2c | 成功认证Java用户: pythontest
2025-08-19 14:21:19.577 | INFO     | 16a998dc866e4672ad77716397f42d2c | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:21:19.578 | INFO     | 16a998dc866e4672ad77716397f42d2c | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:21:19.595 | INFO     | 16a998dc866e4672ad77716397f42d2c | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:21:19.597 | INFO     | 16a998dc866e4672ad77716397f42d2c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 30.377ms
2025-08-19 14:21:32.620 | INFO     | fc13ced60cca4cedad920c63ef28db25 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:21:32.622 | INFO     | eec034da62584babbb252f02808ec3a5 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:21:32.623 | INFO     | fc13ced60cca4cedad920c63ef28db25 | 成功认证Java用户: pythontest
2025-08-19 14:21:32.624 | INFO     | eec034da62584babbb252f02808ec3a5 | 成功认证Java用户: pythontest
2025-08-19 14:21:32.635 | INFO     | eec034da62584babbb252f02808ec3a5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:21:32.636 | INFO     | eec034da62584babbb252f02808ec3a5 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:21:32.641 | INFO     | fc13ced60cca4cedad920c63ef28db25 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:21:32.645 | INFO     | fc13ced60cca4cedad920c63ef28db25 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 24.867ms
2025-08-19 14:21:32.648 | INFO     | d06ace59b6024833ad4440ba08a68651 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:21:32.650 | INFO     | d06ace59b6024833ad4440ba08a68651 | 成功认证Java用户: pythontest
2025-08-19 14:21:32.654 | INFO     | eec034da62584babbb252f02808ec3a5 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:21:32.658 | INFO     | eec034da62584babbb252f02808ec3a5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 36.559ms
2025-08-19 14:21:32.661 | INFO     | d06ace59b6024833ad4440ba08a68651 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:21:32.661 | INFO     | d06ace59b6024833ad4440ba08a68651 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:21:32.679 | INFO     | d06ace59b6024833ad4440ba08a68651 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:21:32.682 | INFO     | d06ace59b6024833ad4440ba08a68651 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 33.771ms
2025-08-19 14:21:41.376 | INFO     | 2a57d52fef584dbab281defcd054a205 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:21:41.377 | INFO     | 2a57d52fef584dbab281defcd054a205 | 成功认证Java用户: pythontest
2025-08-19 14:21:41.383 | INFO     | 2a57d52fef584dbab281defcd054a205 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:21:41.384 | INFO     | 2a57d52fef584dbab281defcd054a205 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 14:21:41.384 | INFO     | 2a57d52fef584dbab281defcd054a205 | 创建知识库请求数据: {'name': 'pythontest4', 'description': '4内容啊'}
2025-08-19 14:21:41.586 | INFO     | 2a57d52fef584dbab281defcd054a205 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 14:21:41.587 | INFO     | 2a57d52fef584dbab281defcd054a205 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 211.980ms
2025-08-19 14:21:41.599 | INFO     | 77c3203a2b7d419d82c3e7d60ae6ad33 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:21:41.601 | INFO     | 77c3203a2b7d419d82c3e7d60ae6ad33 | 成功认证Java用户: pythontest
2025-08-19 14:21:41.607 | INFO     | 77c3203a2b7d419d82c3e7d60ae6ad33 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:21:41.608 | INFO     | 77c3203a2b7d419d82c3e7d60ae6ad33 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:21:41.621 | INFO     | 77c3203a2b7d419d82c3e7d60ae6ad33 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:21:41.623 | INFO     | 77c3203a2b7d419d82c3e7d60ae6ad33 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 24.316ms
2025-08-19 14:21:41.625 | INFO     | 4e22b956e97a4ac581704639490accf2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:21:41.626 | INFO     | 4e22b956e97a4ac581704639490accf2 | 成功认证Java用户: pythontest
2025-08-19 14:21:41.631 | INFO     | 4e22b956e97a4ac581704639490accf2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:21:41.632 | INFO     | 4e22b956e97a4ac581704639490accf2 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:21:41.650 | INFO     | 4e22b956e97a4ac581704639490accf2 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:21:41.651 | INFO     | 4e22b956e97a4ac581704639490accf2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 26.095ms
2025-08-19 14:21:53.362 | INFO     | 8b7ed9374ea74a7d95d82e0ff66bb9ae | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:21:53.363 | INFO     | 8b7ed9374ea74a7d95d82e0ff66bb9ae | 成功认证Java用户: pythontest
2025-08-19 14:21:53.370 | INFO     | 8b7ed9374ea74a7d95d82e0ff66bb9ae | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:21:53.370 | INFO     | 8b7ed9374ea74a7d95d82e0ff66bb9ae | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-19 14:21:53.946 | INFO     | 8b7ed9374ea74a7d95d82e0ff66bb9ae | HTTP Request: DELETE http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 14:21:53.948 | INFO     | 8b7ed9374ea74a7d95d82e0ff66bb9ae | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 586.871ms
2025-08-19 14:21:53.955 | INFO     | 879261a592464d7f998d13f96da6475d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:21:53.957 | INFO     | 879261a592464d7f998d13f96da6475d | 成功认证Java用户: pythontest
2025-08-19 14:21:53.964 | INFO     | 879261a592464d7f998d13f96da6475d | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:21:53.964 | INFO     | 879261a592464d7f998d13f96da6475d | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:21:53.982 | INFO     | 879261a592464d7f998d13f96da6475d | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:21:53.983 | INFO     | 879261a592464d7f998d13f96da6475d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 27.036ms
2025-08-19 14:21:53.984 | INFO     | 574447c51135437ca40d170986d37c4b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:21:53.985 | INFO     | 574447c51135437ca40d170986d37c4b | 成功认证Java用户: pythontest
2025-08-19 14:21:53.990 | INFO     | 574447c51135437ca40d170986d37c4b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:21:53.990 | INFO     | 574447c51135437ca40d170986d37c4b | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:21:54.005 | INFO     | 574447c51135437ca40d170986d37c4b | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:21:54.006 | INFO     | 574447c51135437ca40d170986d37c4b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 21.940ms
2025-08-19 14:22:00.493 | INFO     | c94ae10950e04e15bb2b47117c67d620 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:22:00.494 | INFO     | c94ae10950e04e15bb2b47117c67d620 | 成功认证Java用户: pythontest
2025-08-19 14:22:00.508 | INFO     | c94ae10950e04e15bb2b47117c67d620 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:22:00.510 | INFO     | c94ae10950e04e15bb2b47117c67d620 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 16.916ms
2025-08-19 14:22:02.071 | INFO     | 7e15327e50604878b236de85adc69238 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:22:02.072 | INFO     | 7e15327e50604878b236de85adc69238 | 成功认证Java用户: pythontest
2025-08-19 14:22:02.078 | INFO     | 7e15327e50604878b236de85adc69238 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:22:02.078 | INFO     | 7e15327e50604878b236de85adc69238 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:22:02.091 | INFO     | 7e15327e50604878b236de85adc69238 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:22:02.092 | INFO     | 7e15327e50604878b236de85adc69238 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 22.129ms
2025-08-19 14:22:02.916 | INFO     | 8fbeff39a289474a8a20d2ae872b87f6 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:22:02.917 | INFO     | 8fbeff39a289474a8a20d2ae872b87f6 | 成功认证Java用户: pythontest
2025-08-19 14:22:02.922 | INFO     | 8fbeff39a289474a8a20d2ae872b87f6 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:22:02.923 | INFO     | 8fbeff39a289474a8a20d2ae872b87f6 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:22:02.938 | INFO     | 8fbeff39a289474a8a20d2ae872b87f6 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:22:02.940 | INFO     | 8fbeff39a289474a8a20d2ae872b87f6 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 23.718ms
2025-08-19 14:22:51.901 | INFO     | c272272851ea4d03936bd97b209ad124 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:22:51.903 | INFO     | c272272851ea4d03936bd97b209ad124 | 成功认证Java用户: pythontest
2025-08-19 14:22:51.909 | INFO     | c272272851ea4d03936bd97b209ad124 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:22:51.910 | INFO     | c272272851ea4d03936bd97b209ad124 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 14:22:51.924 | INFO     | c272272851ea4d03936bd97b209ad124 | HTTP Request: GET http://*************:6610/api/v1/datasets?id=87a3346677ee11f0a8f37e63526e5b16 "HTTP/1.1 200 OK"
2025-08-19 14:22:51.925 | INFO     | c272272851ea4d03936bd97b209ad124 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/87a3346677ee11f0a8f37e63526e5b16 | 23.852ms
2025-08-19 14:22:52.738 | INFO     | 74ec2f8aca7c4500af6b8a4e59a5e4fb | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:22:52.739 | INFO     | 74ec2f8aca7c4500af6b8a4e59a5e4fb | 成功认证Java用户: pythontest
2025-08-19 14:22:52.744 | INFO     | 74ec2f8aca7c4500af6b8a4e59a5e4fb | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:22:52.745 | INFO     | 74ec2f8aca7c4500af6b8a4e59a5e4fb | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 14:22:52.761 | INFO     | 74ec2f8aca7c4500af6b8a4e59a5e4fb | HTTP Request: GET http://*************:6610/api/v1/datasets?id=87a3346677ee11f0a8f37e63526e5b16 "HTTP/1.1 200 OK"
2025-08-19 14:22:52.762 | INFO     | 74ec2f8aca7c4500af6b8a4e59a5e4fb | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/87a3346677ee11f0a8f37e63526e5b16 | 23.897ms
2025-08-19 14:23:16.938 | INFO     | 28445ecdc1954b7680ef9a5081fc9e9c | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:23:16.940 | INFO     | 28445ecdc1954b7680ef9a5081fc9e9c | 成功认证Java用户: pythontest
2025-08-19 14:23:16.948 | INFO     | 28445ecdc1954b7680ef9a5081fc9e9c | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:23:16.949 | INFO     | 28445ecdc1954b7680ef9a5081fc9e9c | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 14:23:16.949 | INFO     | 28445ecdc1954b7680ef9a5081fc9e9c | 创建知识库请求数据: {'name': 'pythontest4', 'description': ''}
2025-08-19 14:23:17.138 | INFO     | 28445ecdc1954b7680ef9a5081fc9e9c | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 14:23:17.140 | INFO     | 28445ecdc1954b7680ef9a5081fc9e9c | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 201.046ms
2025-08-19 14:23:17.150 | INFO     | eef1d57a95fe453c90a5ab8ac98c352a | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:23:17.152 | INFO     | eef1d57a95fe453c90a5ab8ac98c352a | 成功认证Java用户: pythontest
2025-08-19 14:23:17.160 | INFO     | eef1d57a95fe453c90a5ab8ac98c352a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:23:17.161 | INFO     | eef1d57a95fe453c90a5ab8ac98c352a | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:23:17.177 | INFO     | eef1d57a95fe453c90a5ab8ac98c352a | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:23:17.178 | INFO     | eef1d57a95fe453c90a5ab8ac98c352a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 27.735ms
2025-08-19 14:23:17.181 | INFO     | 0aa85e5e8bfe460eaf39d7e1bd4c7341 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:23:17.182 | INFO     | 0aa85e5e8bfe460eaf39d7e1bd4c7341 | 成功认证Java用户: pythontest
2025-08-19 14:23:17.188 | INFO     | 0aa85e5e8bfe460eaf39d7e1bd4c7341 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:23:17.188 | INFO     | 0aa85e5e8bfe460eaf39d7e1bd4c7341 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:23:17.203 | INFO     | 0aa85e5e8bfe460eaf39d7e1bd4c7341 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:23:17.204 | INFO     | 0aa85e5e8bfe460eaf39d7e1bd4c7341 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 24.255ms
2025-08-19 14:23:20.626 | INFO     | f2d1c3fbb7984f08b921e5bdf3a75e69 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:23:20.628 | INFO     | f2d1c3fbb7984f08b921e5bdf3a75e69 | 成功认证Java用户: pythontest
2025-08-19 14:23:20.633 | INFO     | f2d1c3fbb7984f08b921e5bdf3a75e69 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:23:20.633 | INFO     | f2d1c3fbb7984f08b921e5bdf3a75e69 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 14:23:20.648 | INFO     | f2d1c3fbb7984f08b921e5bdf3a75e69 | HTTP Request: GET http://*************:6610/api/v1/datasets?id=fe9f520e7cc411f0bdabea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 14:23:20.649 | INFO     | f2d1c3fbb7984f08b921e5bdf3a75e69 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/fe9f520e7cc411f0bdabea5dc8d5776c | 22.656ms
2025-08-19 14:23:33.041 | INFO     | 6f6282c4e06e49828af52c127de13bc7 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:23:33.043 | INFO     | 6f6282c4e06e49828af52c127de13bc7 | 成功认证Java用户: pythontest
2025-08-19 14:23:33.049 | INFO     | 6f6282c4e06e49828af52c127de13bc7 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:23:33.050 | INFO     | 6f6282c4e06e49828af52c127de13bc7 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-19 14:23:33.178 | INFO     | 6f6282c4e06e49828af52c127de13bc7 | HTTP Request: PUT http://*************:6610/api/v1/datasets/fe9f520e7cc411f0bdabea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 14:23:33.179 | INFO     | 6f6282c4e06e49828af52c127de13bc7 | 127.0.0.1       | PUT      | 200    | /api/iot/v1/knowledge-base/fe9f520e7cc411f0bdabea5dc8d5776c | 138.230ms
2025-08-19 14:23:33.189 | INFO     | 34c604f45cd84fdfa5a58912e3dc69b1 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:23:33.191 | INFO     | 34c604f45cd84fdfa5a58912e3dc69b1 | 成功认证Java用户: pythontest
2025-08-19 14:23:33.196 | INFO     | 34c604f45cd84fdfa5a58912e3dc69b1 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:23:33.197 | INFO     | 34c604f45cd84fdfa5a58912e3dc69b1 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:23:33.213 | INFO     | 34c604f45cd84fdfa5a58912e3dc69b1 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:23:33.215 | INFO     | 34c604f45cd84fdfa5a58912e3dc69b1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 25.965ms
2025-08-19 14:23:33.217 | INFO     | 9458d1ed8ed04eb1bd882d3e1526169d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:23:33.219 | INFO     | 9458d1ed8ed04eb1bd882d3e1526169d | 成功认证Java用户: pythontest
2025-08-19 14:23:33.225 | INFO     | 9458d1ed8ed04eb1bd882d3e1526169d | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:23:33.226 | INFO     | 9458d1ed8ed04eb1bd882d3e1526169d | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:23:33.243 | INFO     | 9458d1ed8ed04eb1bd882d3e1526169d | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:23:33.244 | INFO     | 9458d1ed8ed04eb1bd882d3e1526169d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 27.059ms
2025-08-19 14:23:38.090 | INFO     | 55db606f66c0441db57f81925b449ef3 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:23:38.092 | INFO     | 55db606f66c0441db57f81925b449ef3 | 成功认证Java用户: pythontest
2025-08-19 14:23:38.098 | INFO     | 55db606f66c0441db57f81925b449ef3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:23:38.099 | INFO     | 55db606f66c0441db57f81925b449ef3 | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-19 14:23:38.283 | INFO     | 55db606f66c0441db57f81925b449ef3 | HTTP Request: DELETE http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 14:23:38.284 | INFO     | 55db606f66c0441db57f81925b449ef3 | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 194.441ms
2025-08-19 14:23:38.291 | INFO     | 96754bdd81164558a8ce46ae51dfbd09 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:23:38.292 | INFO     | 5a128b5709b642579eb0f5310cbe63ec | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:23:38.293 | INFO     | 96754bdd81164558a8ce46ae51dfbd09 | 成功认证Java用户: pythontest
2025-08-19 14:23:38.294 | INFO     | 5a128b5709b642579eb0f5310cbe63ec | 成功认证Java用户: pythontest
2025-08-19 14:23:38.299 | INFO     | 96754bdd81164558a8ce46ae51dfbd09 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:23:38.300 | INFO     | 96754bdd81164558a8ce46ae51dfbd09 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:23:38.304 | INFO     | 5a128b5709b642579eb0f5310cbe63ec | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:23:38.304 | INFO     | 5a128b5709b642579eb0f5310cbe63ec | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:23:38.324 | INFO     | 96754bdd81164558a8ce46ae51dfbd09 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:23:38.325 | INFO     | 5a128b5709b642579eb0f5310cbe63ec | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:23:38.327 | INFO     | 96754bdd81164558a8ce46ae51dfbd09 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 35.609ms
2025-08-19 14:23:38.327 | INFO     | 5a128b5709b642579eb0f5310cbe63ec | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 35.914ms
2025-08-19 14:23:40.738 | INFO     | 244b0bf60eb445f39b2412154c4f444a | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:23:40.739 | INFO     | 244b0bf60eb445f39b2412154c4f444a | 成功认证Java用户: pythontest
2025-08-19 14:23:40.754 | INFO     | 244b0bf60eb445f39b2412154c4f444a | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:23:40.755 | INFO     | 244b0bf60eb445f39b2412154c4f444a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 16.887ms
2025-08-19 14:23:44.244 | INFO     | 5505f0436de441c79b3c872f6d8c3ad9 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:23:44.245 | INFO     | 5505f0436de441c79b3c872f6d8c3ad9 | 成功认证Java用户: pythontest
2025-08-19 14:23:44.251 | INFO     | 5505f0436de441c79b3c872f6d8c3ad9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:23:44.252 | INFO     | 5505f0436de441c79b3c872f6d8c3ad9 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:23:44.268 | INFO     | 5505f0436de441c79b3c872f6d8c3ad9 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:23:44.269 | INFO     | 5505f0436de441c79b3c872f6d8c3ad9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 25.330ms
2025-08-19 14:23:44.288 | INFO     | f93453f29f1a4179aeae2c9251a4f901 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:23:44.290 | INFO     | f93453f29f1a4179aeae2c9251a4f901 | 成功认证Java用户: pythontest
2025-08-19 14:23:44.299 | INFO     | f93453f29f1a4179aeae2c9251a4f901 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:23:44.300 | INFO     | f93453f29f1a4179aeae2c9251a4f901 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:23:44.317 | INFO     | f93453f29f1a4179aeae2c9251a4f901 | HTTP Request: GET http://*************:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:23:44.319 | INFO     | f93453f29f1a4179aeae2c9251a4f901 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/87a3346677ee11f0a8f37e63526e5b16/list/page=1&page_size=20&orderby=create_time&desc=true | 30.955ms
2025-08-19 14:23:52.021 | INFO     | 34e9f67d39534ecbb7f0ed87cf1a600f | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:23:52.023 | INFO     | 34e9f67d39534ecbb7f0ed87cf1a600f | 成功认证Java用户: pythontest
2025-08-19 14:23:52.028 | INFO     | 34e9f67d39534ecbb7f0ed87cf1a600f | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:23:52.028 | INFO     | 34e9f67d39534ecbb7f0ed87cf1a600f | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 14:23:52.082 | INFO     | 34e9f67d39534ecbb7f0ed87cf1a600f | HTTP Request: GET http://*************:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16/documents/a5f2f8ac78de11f080f07e63526e5b16 "HTTP/1.1 200 OK"
2025-08-19 14:23:52.138 | INFO     | 34e9f67d39534ecbb7f0ed87cf1a600f | 文档预览调试 - doc_id: a5f2f8ac78de11f080f07e63526e5b16
2025-08-19 14:23:52.139 | INFO     | 34e9f67d39534ecbb7f0ed87cf1a600f | doc_name: 'test.xlsx', doc_data keys: ['content', 'content_type', 'status_code']
2025-08-19 14:23:52.139 | INFO     | 34e9f67d39534ecbb7f0ed87cf1a600f | RAGFlow返回的文档信息: {'content_type': 'application/octet-stream', 'status_code': 200}
2025-08-19 14:23:52.139 | INFO     | 34e9f67d39534ecbb7f0ed87cf1a600f | 根据文件头检测到Office文档格式(ZIP)，修正类型为office
2025-08-19 14:23:52.139 | INFO     | 34e9f67d39534ecbb7f0ed87cf1a600f | 最终识别的文档类型: office
2025-08-19 14:23:52.139 | INFO     | 34e9f67d39534ecbb7f0ed87cf1a600f | 检测到Office文档: test.xlsx，返回原始文档URL用于vue-office预览
2025-08-19 14:23:52.140 | INFO     | 34e9f67d39534ecbb7f0ed87cf1a600f | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/87a3346677ee11f0a8f37e63526e5b16/a5f2f8ac78de11f080f07e63526e5b16/preview/doc_name=test.xlsx | 119.492ms
2025-08-19 14:23:52.280 | INFO     | f87e4a1833b545d5b0c2d246b20ed048 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:23:52.281 | INFO     | f87e4a1833b545d5b0c2d246b20ed048 | 成功认证Java用户: pythontest
2025-08-19 14:23:52.287 | INFO     | f87e4a1833b545d5b0c2d246b20ed048 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:23:52.287 | INFO     | f87e4a1833b545d5b0c2d246b20ed048 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 14:23:52.310 | INFO     | f87e4a1833b545d5b0c2d246b20ed048 | HTTP Request: GET http://*************:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16/documents/a5f2f8ac78de11f080f07e63526e5b16 "HTTP/1.1 200 OK"
2025-08-19 14:23:52.381 | INFO     | f87e4a1833b545d5b0c2d246b20ed048 | HTTP Request: GET http://*************:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16/documents/a5f2f8ac78de11f080f07e63526e5b16 "HTTP/1.1 200 OK"
2025-08-19 14:23:52.430 | INFO     | f87e4a1833b545d5b0c2d246b20ed048 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/87a3346677ee11f0a8f37e63526e5b16/a5f2f8ac78de11f080f07e63526e5b16/content | 149.555ms
2025-08-19 14:23:58.428 | INFO     | 3311717305d14a27bfa2e6c3bf7a8d08 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:23:58.429 | INFO     | 3311717305d14a27bfa2e6c3bf7a8d08 | 成功认证Java用户: pythontest
2025-08-19 14:23:58.434 | INFO     | 3311717305d14a27bfa2e6c3bf7a8d08 | 127.0.0.1       | POST     | 422    | /api/iot/v1/documents/87a3346677ee11f0a8f37e63526e5b16/9ec31c2478de11f080f07e63526e5b16/parse | 6.618ms
2025-08-19 14:24:00.158 | INFO     | 66603cc522b145ce837b1eb85c504296 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:24:00.160 | INFO     | 66603cc522b145ce837b1eb85c504296 | 成功认证Java用户: pythontest
2025-08-19 14:24:00.165 | INFO     | 66603cc522b145ce837b1eb85c504296 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:24:00.165 | INFO     | 66603cc522b145ce837b1eb85c504296 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 14:24:00.317 | INFO     | 66603cc522b145ce837b1eb85c504296 | HTTP Request: GET http://*************:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16/documents/9ec31c2478de11f080f07e63526e5b16 "HTTP/1.1 200 OK"
2025-08-19 14:24:00.791 | INFO     | 66603cc522b145ce837b1eb85c504296 | 文档预览调试 - doc_id: 9ec31c2478de11f080f07e63526e5b16
2025-08-19 14:24:00.792 | INFO     | 66603cc522b145ce837b1eb85c504296 | doc_name: 'test.pptx', doc_data keys: ['content', 'content_type', 'status_code']
2025-08-19 14:24:00.792 | INFO     | 66603cc522b145ce837b1eb85c504296 | RAGFlow返回的文档信息: {'content_type': 'application/octet-stream', 'status_code': 200}
2025-08-19 14:24:00.792 | INFO     | 66603cc522b145ce837b1eb85c504296 | 根据文件头检测到Office文档格式(ZIP)，修正类型为office
2025-08-19 14:24:00.792 | INFO     | 66603cc522b145ce837b1eb85c504296 | 最终识别的文档类型: office
2025-08-19 14:24:00.792 | INFO     | 66603cc522b145ce837b1eb85c504296 | 检测到Office文档: test.pptx，返回原始文档URL用于vue-office预览
2025-08-19 14:24:00.793 | INFO     | 66603cc522b145ce837b1eb85c504296 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/87a3346677ee11f0a8f37e63526e5b16/9ec31c2478de11f080f07e63526e5b16/preview/doc_name=test.pptx | 635.353ms
2025-08-19 14:24:00.802 | INFO     | 37efe4655160402ab3cdbaee4b6b6e00 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:24:00.803 | INFO     | 37efe4655160402ab3cdbaee4b6b6e00 | 成功认证Java用户: pythontest
2025-08-19 14:24:00.808 | INFO     | 37efe4655160402ab3cdbaee4b6b6e00 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:24:00.808 | INFO     | 37efe4655160402ab3cdbaee4b6b6e00 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 14:24:00.852 | INFO     | 37efe4655160402ab3cdbaee4b6b6e00 | HTTP Request: GET http://*************:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16/documents/9ec31c2478de11f080f07e63526e5b16 "HTTP/1.1 200 OK"
2025-08-19 14:24:01.419 | INFO     | 37efe4655160402ab3cdbaee4b6b6e00 | HTTP Request: GET http://*************:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16/documents/9ec31c2478de11f080f07e63526e5b16 "HTTP/1.1 200 OK"
2025-08-19 14:24:01.966 | INFO     | 37efe4655160402ab3cdbaee4b6b6e00 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/87a3346677ee11f0a8f37e63526e5b16/9ec31c2478de11f080f07e63526e5b16/content | 1164.800ms
2025-08-19 14:24:04.679 | INFO     | c4cdb6d2dd8e499f90cb4eab7bf6889e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:24:04.680 | INFO     | c4cdb6d2dd8e499f90cb4eab7bf6889e | 成功认证Java用户: pythontest
2025-08-19 14:24:04.685 | INFO     | c4cdb6d2dd8e499f90cb4eab7bf6889e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:24:04.686 | INFO     | c4cdb6d2dd8e499f90cb4eab7bf6889e | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 14:24:04.756 | INFO     | c4cdb6d2dd8e499f90cb4eab7bf6889e | HTTP Request: GET http://*************:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16/documents/9ec31c2478de11f080f07e63526e5b16 "HTTP/1.1 200 OK"
2025-08-19 14:24:05.242 | INFO     | c4cdb6d2dd8e499f90cb4eab7bf6889e | 文档预览调试 - doc_id: 9ec31c2478de11f080f07e63526e5b16
2025-08-19 14:24:05.242 | INFO     | c4cdb6d2dd8e499f90cb4eab7bf6889e | doc_name: 'test.pptx', doc_data keys: ['content', 'content_type', 'status_code']
2025-08-19 14:24:05.243 | INFO     | c4cdb6d2dd8e499f90cb4eab7bf6889e | RAGFlow返回的文档信息: {'content_type': 'application/octet-stream', 'status_code': 200}
2025-08-19 14:24:05.243 | INFO     | c4cdb6d2dd8e499f90cb4eab7bf6889e | 根据文件头检测到Office文档格式(ZIP)，修正类型为office
2025-08-19 14:24:05.243 | INFO     | c4cdb6d2dd8e499f90cb4eab7bf6889e | 最终识别的文档类型: office
2025-08-19 14:24:05.243 | INFO     | c4cdb6d2dd8e499f90cb4eab7bf6889e | 检测到Office文档: test.pptx，返回原始文档URL用于vue-office预览
2025-08-19 14:24:05.243 | INFO     | c4cdb6d2dd8e499f90cb4eab7bf6889e | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/87a3346677ee11f0a8f37e63526e5b16/9ec31c2478de11f080f07e63526e5b16/preview/doc_name=test.pptx | 564.490ms
2025-08-19 14:24:32.458 | INFO     | 81436407f30b413d8c5ecd7cb274abe9 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:24:32.460 | INFO     | 81436407f30b413d8c5ecd7cb274abe9 | 成功认证Java用户: pythontest
2025-08-19 14:24:32.462 | INFO     | 81436407f30b413d8c5ecd7cb274abe9 | 127.0.0.1       | POST     | 422    | /api/iot/v1/documents/87a3346677ee11f0a8f37e63526e5b16/9ec31c2478de11f080f07e63526e5b16/parse | 3.367ms
2025-08-19 14:24:33.643 | INFO     | 4960fe552ed34877ae482fe6ce511dd2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:24:33.645 | INFO     | 4960fe552ed34877ae482fe6ce511dd2 | 成功认证Java用户: pythontest
2025-08-19 14:24:33.649 | INFO     | 4960fe552ed34877ae482fe6ce511dd2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:24:33.650 | INFO     | 4960fe552ed34877ae482fe6ce511dd2 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 14:24:33.710 | INFO     | 4960fe552ed34877ae482fe6ce511dd2 | HTTP Request: GET http://*************:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16/documents/9ec31c2478de11f080f07e63526e5b16 "HTTP/1.1 200 OK"
2025-08-19 14:24:34.226 | INFO     | 4960fe552ed34877ae482fe6ce511dd2 | 文档预览调试 - doc_id: 9ec31c2478de11f080f07e63526e5b16
2025-08-19 14:24:34.226 | INFO     | 4960fe552ed34877ae482fe6ce511dd2 | doc_name: 'test.pptx', doc_data keys: ['content', 'content_type', 'status_code']
2025-08-19 14:24:34.226 | INFO     | 4960fe552ed34877ae482fe6ce511dd2 | RAGFlow返回的文档信息: {'content_type': 'application/octet-stream', 'status_code': 200}
2025-08-19 14:24:34.227 | INFO     | 4960fe552ed34877ae482fe6ce511dd2 | 根据文件头检测到Office文档格式(ZIP)，修正类型为office
2025-08-19 14:24:34.227 | INFO     | 4960fe552ed34877ae482fe6ce511dd2 | 最终识别的文档类型: office
2025-08-19 14:24:34.227 | INFO     | 4960fe552ed34877ae482fe6ce511dd2 | 检测到Office文档: test.pptx，返回原始文档URL用于vue-office预览
2025-08-19 14:24:34.227 | INFO     | 4960fe552ed34877ae482fe6ce511dd2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/87a3346677ee11f0a8f37e63526e5b16/9ec31c2478de11f080f07e63526e5b16/preview/doc_name=test.pptx | 584.442ms
2025-08-19 14:24:51.835 | INFO     | 505a2f831ad34a139207cf4b93a12345 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:24:51.837 | INFO     | 505a2f831ad34a139207cf4b93a12345 | 成功认证Java用户: pythontest
2025-08-19 14:24:51.854 | INFO     | 505a2f831ad34a139207cf4b93a12345 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:24:51.855 | INFO     | 505a2f831ad34a139207cf4b93a12345 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 19.699ms
2025-08-19 14:24:51.858 | INFO     | ec0c51ccb62d4dd18c5cbfd0e322c716 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:24:51.860 | INFO     | ec0c51ccb62d4dd18c5cbfd0e322c716 | 成功认证Java用户: pythontest
2025-08-19 14:24:51.867 | INFO     | ec0c51ccb62d4dd18c5cbfd0e322c716 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:24:51.868 | INFO     | ec0c51ccb62d4dd18c5cbfd0e322c716 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:24:51.888 | INFO     | ec0c51ccb62d4dd18c5cbfd0e322c716 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:24:51.890 | INFO     | ec0c51ccb62d4dd18c5cbfd0e322c716 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 32.129ms
2025-08-19 14:24:51.893 | INFO     | 218a6818358f4e338be7ab4a879ce099 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:24:51.894 | INFO     | 218a6818358f4e338be7ab4a879ce099 | 成功认证Java用户: pythontest
2025-08-19 14:24:51.900 | INFO     | 218a6818358f4e338be7ab4a879ce099 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:24:51.901 | INFO     | 218a6818358f4e338be7ab4a879ce099 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:24:51.916 | INFO     | 218a6818358f4e338be7ab4a879ce099 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:24:51.917 | INFO     | 218a6818358f4e338be7ab4a879ce099 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 24.633ms
2025-08-19 14:25:27.087 | INFO     | c2a1302211734fe885cf95a6bb9480fa | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:25:27.088 | INFO     | c2a1302211734fe885cf95a6bb9480fa | 成功认证Java用户: pythontest
2025-08-19 14:25:27.094 | INFO     | c2a1302211734fe885cf95a6bb9480fa | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:25:27.095 | INFO     | c2a1302211734fe885cf95a6bb9480fa | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:25:27.110 | INFO     | c2a1302211734fe885cf95a6bb9480fa | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:25:27.111 | INFO     | c2a1302211734fe885cf95a6bb9480fa | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 24.311ms
2025-08-19 14:25:27.126 | INFO     | 0cd4e11e03f54583a6b52d2b0d475fb1 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:25:27.127 | INFO     | 0cd4e11e03f54583a6b52d2b0d475fb1 | 成功认证Java用户: pythontest
2025-08-19 14:25:27.134 | INFO     | 0cd4e11e03f54583a6b52d2b0d475fb1 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:25:27.135 | INFO     | 0cd4e11e03f54583a6b52d2b0d475fb1 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:25:27.151 | INFO     | 0cd4e11e03f54583a6b52d2b0d475fb1 | HTTP Request: GET http://*************:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:25:27.153 | INFO     | 0cd4e11e03f54583a6b52d2b0d475fb1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/87a3346677ee11f0a8f37e63526e5b16/list/page=1&page_size=20&orderby=create_time&desc=true | 27.535ms
2025-08-19 14:29:49.422 | INFO     | 96aeb4ea98d54a9f939ee4f2a7ac014d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:29:49.423 | INFO     | 6a97dec7e2ed4e4ab92f1e85e6699f60 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:29:49.424 | INFO     | 5bbc2c88d9b14e4a85aaecdc8461f291 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:29:49.425 | INFO     | 96aeb4ea98d54a9f939ee4f2a7ac014d | 成功认证Java用户: pythontest
2025-08-19 14:29:49.426 | INFO     | 6a97dec7e2ed4e4ab92f1e85e6699f60 | 成功认证Java用户: pythontest
2025-08-19 14:29:49.427 | INFO     | 5bbc2c88d9b14e4a85aaecdc8461f291 | 成功认证Java用户: pythontest
2025-08-19 14:29:49.435 | INFO     | 5bbc2c88d9b14e4a85aaecdc8461f291 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:29:49.435 | INFO     | 5bbc2c88d9b14e4a85aaecdc8461f291 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:29:49.438 | INFO     | 6a97dec7e2ed4e4ab92f1e85e6699f60 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:29:49.439 | INFO     | 6a97dec7e2ed4e4ab92f1e85e6699f60 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:29:49.443 | INFO     | 96aeb4ea98d54a9f939ee4f2a7ac014d | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:29:49.444 | INFO     | 96aeb4ea98d54a9f939ee4f2a7ac014d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 22.262ms
2025-08-19 14:29:49.455 | INFO     | 5bbc2c88d9b14e4a85aaecdc8461f291 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:29:49.456 | INFO     | 5bbc2c88d9b14e4a85aaecdc8461f291 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 32.271ms
2025-08-19 14:29:49.459 | INFO     | 6a97dec7e2ed4e4ab92f1e85e6699f60 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:29:49.460 | INFO     | 6a97dec7e2ed4e4ab92f1e85e6699f60 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 37.171ms
2025-08-19 14:29:49.892 | INFO     | ea8d1c9841524f36bc5eb016ee60340f | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:29:49.894 | INFO     | ea8d1c9841524f36bc5eb016ee60340f | 成功认证Java用户: pythontest
2025-08-19 14:29:49.900 | INFO     | ea8d1c9841524f36bc5eb016ee60340f | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:29:49.901 | INFO     | ea8d1c9841524f36bc5eb016ee60340f | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:29:49.914 | INFO     | ea8d1c9841524f36bc5eb016ee60340f | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:29:49.916 | INFO     | ea8d1c9841524f36bc5eb016ee60340f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 23.272ms
2025-08-19 14:29:49.924 | INFO     | 1ec9ba1ff1764d4daaf52018e467396a | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:29:49.925 | INFO     | 1ec9ba1ff1764d4daaf52018e467396a | 成功认证Java用户: pythontest
2025-08-19 14:29:49.932 | INFO     | 1ec9ba1ff1764d4daaf52018e467396a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:29:49.932 | INFO     | 1ec9ba1ff1764d4daaf52018e467396a | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:29:49.948 | INFO     | 1ec9ba1ff1764d4daaf52018e467396a | HTTP Request: GET http://*************:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:29:49.949 | INFO     | 1ec9ba1ff1764d4daaf52018e467396a | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/87a3346677ee11f0a8f37e63526e5b16/list/page=1&page_size=20&orderby=create_time&desc=true | 25.318ms
2025-08-19 14:29:54.952 | INFO     | 5e3fa943f51b4ebaba3765237a3306c2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:29:54.955 | INFO     | 5e3fa943f51b4ebaba3765237a3306c2 | 成功认证Java用户: pythontest
2025-08-19 14:29:54.971 | INFO     | 5e3fa943f51b4ebaba3765237a3306c2 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:29:54.972 | INFO     | 5e3fa943f51b4ebaba3765237a3306c2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 19.401ms
2025-08-19 14:29:54.973 | INFO     | 4f209ccfba154ae5a6040068996dee50 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:29:54.975 | INFO     | 4f209ccfba154ae5a6040068996dee50 | 成功认证Java用户: pythontest
2025-08-19 14:29:54.982 | INFO     | 4f209ccfba154ae5a6040068996dee50 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:29:54.983 | INFO     | 4f209ccfba154ae5a6040068996dee50 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:29:54.999 | INFO     | 4f209ccfba154ae5a6040068996dee50 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:29:55.001 | INFO     | 4f209ccfba154ae5a6040068996dee50 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 27.652ms
2025-08-19 14:29:55.005 | INFO     | d4bdf41fcb9c4ddbafd4ccca57efe1e8 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:29:55.007 | INFO     | d4bdf41fcb9c4ddbafd4ccca57efe1e8 | 成功认证Java用户: pythontest
2025-08-19 14:29:55.015 | INFO     | d4bdf41fcb9c4ddbafd4ccca57efe1e8 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:29:55.016 | INFO     | d4bdf41fcb9c4ddbafd4ccca57efe1e8 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:29:55.033 | INFO     | d4bdf41fcb9c4ddbafd4ccca57efe1e8 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:29:55.035 | INFO     | d4bdf41fcb9c4ddbafd4ccca57efe1e8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 31.584ms
2025-08-19 14:30:01.270 | INFO     | 4982d134b3524033aaaf7330777300ab | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:30:01.272 | INFO     | 4982d134b3524033aaaf7330777300ab | 成功认证Java用户: pythontest
2025-08-19 14:30:01.279 | INFO     | 4982d134b3524033aaaf7330777300ab | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:30:01.280 | INFO     | 4982d134b3524033aaaf7330777300ab | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 14:30:01.280 | INFO     | 4982d134b3524033aaaf7330777300ab | 创建知识库请求数据: {'name': 'pythontest2', 'description': ''}
2025-08-19 14:30:01.416 | INFO     | 4982d134b3524033aaaf7330777300ab | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 14:30:01.417 | INFO     | 4982d134b3524033aaaf7330777300ab | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 147.219ms
2025-08-19 14:30:01.428 | INFO     | cbad573de0a04a19994da93da5dbdb90 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:30:01.428 | INFO     | 09445665f4e3438d91f2d82443c784c2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:30:01.430 | INFO     | cbad573de0a04a19994da93da5dbdb90 | 成功认证Java用户: pythontest
2025-08-19 14:30:01.432 | INFO     | 09445665f4e3438d91f2d82443c784c2 | 成功认证Java用户: pythontest
2025-08-19 14:30:01.443 | INFO     | 09445665f4e3438d91f2d82443c784c2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:30:01.443 | INFO     | 09445665f4e3438d91f2d82443c784c2 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:30:01.448 | INFO     | cbad573de0a04a19994da93da5dbdb90 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:30:01.449 | INFO     | cbad573de0a04a19994da93da5dbdb90 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:30:01.477 | INFO     | 09445665f4e3438d91f2d82443c784c2 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:30:01.478 | INFO     | cbad573de0a04a19994da93da5dbdb90 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:30:01.479 | INFO     | cbad573de0a04a19994da93da5dbdb90 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 51.885ms
2025-08-19 14:30:01.480 | INFO     | 09445665f4e3438d91f2d82443c784c2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 52.336ms
2025-08-19 14:30:04.967 | INFO     | 4304e9153b6e4ca19e802eb6dac4329b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:30:04.969 | INFO     | 4304e9153b6e4ca19e802eb6dac4329b | 成功认证Java用户: pythontest
2025-08-19 14:30:04.976 | INFO     | 4304e9153b6e4ca19e802eb6dac4329b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:30:04.978 | INFO     | 4304e9153b6e4ca19e802eb6dac4329b | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:30:04.994 | INFO     | 4304e9153b6e4ca19e802eb6dac4329b | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:30:04.996 | INFO     | 4304e9153b6e4ca19e802eb6dac4329b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 29.343ms
2025-08-19 14:30:05.063 | INFO     | cacad59f987b45c58b364c7d85d98b7e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:30:05.065 | INFO     | cacad59f987b45c58b364c7d85d98b7e | 成功认证Java用户: pythontest
2025-08-19 14:30:05.073 | INFO     | cacad59f987b45c58b364c7d85d98b7e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:30:05.074 | INFO     | cacad59f987b45c58b364c7d85d98b7e | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:30:05.090 | INFO     | cacad59f987b45c58b364c7d85d98b7e | HTTP Request: GET http://*************:6610/api/v1/datasets/efa077f07cc511f0815aea5dc8d5776c/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:30:05.092 | INFO     | cacad59f987b45c58b364c7d85d98b7e | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/efa077f07cc511f0815aea5dc8d5776c/list/page=1&page_size=20&orderby=create_time&desc=true | 28.532ms
2025-08-19 14:30:21.145 | INFO     | 94a01038de2f449187c0455584912899 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:30:21.147 | INFO     | 94a01038de2f449187c0455584912899 | 成功认证Java用户: pythontest
2025-08-19 14:30:21.154 | INFO     | 94a01038de2f449187c0455584912899 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:30:21.154 | INFO     | 94a01038de2f449187c0455584912899 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:30:21.168 | INFO     | 94a01038de2f449187c0455584912899 | HTTP Request: GET http://*************:6610/api/v1/datasets/efa077f07cc511f0815aea5dc8d5776c/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:30:21.170 | INFO     | 94a01038de2f449187c0455584912899 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/efa077f07cc511f0815aea5dc8d5776c/list/page=1&page_size=20&orderby=create_time&desc=true | 25.763ms
2025-08-19 14:30:21.172 | INFO     | a6ad64093269400995c39bc4923fea30 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:30:21.174 | INFO     | a6ad64093269400995c39bc4923fea30 | 成功认证Java用户: pythontest
2025-08-19 14:30:21.180 | INFO     | a6ad64093269400995c39bc4923fea30 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:30:21.180 | INFO     | a6ad64093269400995c39bc4923fea30 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:30:21.195 | INFO     | a6ad64093269400995c39bc4923fea30 | HTTP Request: GET http://*************:6610/api/v1/datasets/efa077f07cc511f0815aea5dc8d5776c/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:30:21.196 | INFO     | a6ad64093269400995c39bc4923fea30 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/efa077f07cc511f0815aea5dc8d5776c/list/page=1&page_size=100 | 23.872ms
2025-08-19 14:30:42.515 | INFO     | aa782981483f49bdad83abc123c27515 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:30:42.517 | INFO     | aa782981483f49bdad83abc123c27515 | 成功认证Java用户: pythontest
2025-08-19 14:30:42.526 | INFO     | aa782981483f49bdad83abc123c27515 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:30:42.527 | INFO     | aa782981483f49bdad83abc123c27515 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:30:42.544 | INFO     | aa782981483f49bdad83abc123c27515 | HTTP Request: GET http://*************:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:30:42.546 | INFO     | aa782981483f49bdad83abc123c27515 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/87a3346677ee11f0a8f37e63526e5b16/list/page=1&page_size=20&orderby=create_time&desc=true | 31.432ms
2025-08-19 14:30:43.734 | INFO     | b2d8b1dd41d64c5bad82a939e448563b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:30:43.736 | INFO     | b2d8b1dd41d64c5bad82a939e448563b | 成功认证Java用户: pythontest
2025-08-19 14:30:43.743 | INFO     | b2d8b1dd41d64c5bad82a939e448563b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:30:43.744 | INFO     | b2d8b1dd41d64c5bad82a939e448563b | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:30:43.760 | INFO     | b2d8b1dd41d64c5bad82a939e448563b | HTTP Request: GET http://*************:6610/api/v1/datasets/efa077f07cc511f0815aea5dc8d5776c/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:30:43.761 | INFO     | b2d8b1dd41d64c5bad82a939e448563b | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/efa077f07cc511f0815aea5dc8d5776c/list/page=1&page_size=20&orderby=create_time&desc=true | 27.821ms
2025-08-19 14:30:44.738 | INFO     | b9bb2d2a2cc54e03a1570820fd1b137b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:30:44.740 | INFO     | b9bb2d2a2cc54e03a1570820fd1b137b | 成功认证Java用户: pythontest
2025-08-19 14:30:44.746 | INFO     | b9bb2d2a2cc54e03a1570820fd1b137b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:30:44.747 | INFO     | b9bb2d2a2cc54e03a1570820fd1b137b | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:30:44.766 | INFO     | b9bb2d2a2cc54e03a1570820fd1b137b | HTTP Request: GET http://*************:6610/api/v1/datasets/67c9904a346411f089c72e091ca444e4/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:30:44.768 | INFO     | b9bb2d2a2cc54e03a1570820fd1b137b | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/67c9904a346411f089c72e091ca444e4/list/page=1&page_size=20&orderby=create_time&desc=true | 29.676ms
2025-08-19 14:30:45.787 | INFO     | 1a2c0d312fad4ad090266e995658b27b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:30:45.789 | INFO     | 1a2c0d312fad4ad090266e995658b27b | 成功认证Java用户: pythontest
2025-08-19 14:30:45.798 | INFO     | 1a2c0d312fad4ad090266e995658b27b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:30:45.799 | INFO     | 1a2c0d312fad4ad090266e995658b27b | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:30:45.820 | INFO     | 1a2c0d312fad4ad090266e995658b27b | HTTP Request: GET http://*************:6610/api/v1/datasets/0d677ada090e11f083fcf6ceb56a6e4e/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:30:45.823 | INFO     | 1a2c0d312fad4ad090266e995658b27b | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/0d677ada090e11f083fcf6ceb56a6e4e/list/page=1&page_size=20&orderby=create_time&desc=true | 36.370ms
2025-08-19 14:30:47.612 | INFO     | 17e355b856834d81b46eb032965bf629 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:30:47.614 | INFO     | 17e355b856834d81b46eb032965bf629 | 成功认证Java用户: pythontest
2025-08-19 14:30:47.622 | INFO     | 17e355b856834d81b46eb032965bf629 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:30:47.623 | INFO     | 17e355b856834d81b46eb032965bf629 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:30:47.639 | INFO     | 17e355b856834d81b46eb032965bf629 | HTTP Request: GET http://*************:6610/api/v1/datasets/237eca96055b11f0a7aaf6ceb56a6e4e/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:30:47.642 | INFO     | 17e355b856834d81b46eb032965bf629 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/237eca96055b11f0a7aaf6ceb56a6e4e/list/page=1&page_size=20&orderby=create_time&desc=true | 29.448ms
2025-08-19 14:30:48.667 | INFO     | ec5ccb75f0f6469fa295e6b341e6a035 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:30:48.669 | INFO     | ec5ccb75f0f6469fa295e6b341e6a035 | 成功认证Java用户: pythontest
2025-08-19 14:30:48.678 | INFO     | ec5ccb75f0f6469fa295e6b341e6a035 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:30:48.679 | INFO     | ec5ccb75f0f6469fa295e6b341e6a035 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:30:48.697 | INFO     | ec5ccb75f0f6469fa295e6b341e6a035 | HTTP Request: GET http://*************:6610/api/v1/datasets/efa077f07cc511f0815aea5dc8d5776c/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:30:48.699 | INFO     | ec5ccb75f0f6469fa295e6b341e6a035 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/efa077f07cc511f0815aea5dc8d5776c/list/page=1&page_size=20&orderby=create_time&desc=true | 31.847ms
2025-08-19 14:30:57.434 | INFO     | 9d5a795f2b0c47ffa143040afc63f6b1 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:30:57.437 | INFO     | 9d5a795f2b0c47ffa143040afc63f6b1 | 成功认证Java用户: pythontest
2025-08-19 14:30:57.448 | INFO     | 9d5a795f2b0c47ffa143040afc63f6b1 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:30:57.450 | INFO     | 9d5a795f2b0c47ffa143040afc63f6b1 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:30:57.470 | INFO     | 9d5a795f2b0c47ffa143040afc63f6b1 | HTTP Request: GET http://*************:6610/api/v1/datasets/efa077f07cc511f0815aea5dc8d5776c/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:30:57.473 | INFO     | 9d5a795f2b0c47ffa143040afc63f6b1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/efa077f07cc511f0815aea5dc8d5776c/list/page=1&page_size=20&orderby=create_time&desc=true | 39.508ms
2025-08-19 14:30:57.476 | INFO     | 7c5714d68467455eb1d8094ec827fec3 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:30:57.478 | INFO     | 7c5714d68467455eb1d8094ec827fec3 | 成功认证Java用户: pythontest
2025-08-19 14:30:57.487 | INFO     | 7c5714d68467455eb1d8094ec827fec3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:30:57.488 | INFO     | 7c5714d68467455eb1d8094ec827fec3 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:30:57.506 | INFO     | 7c5714d68467455eb1d8094ec827fec3 | HTTP Request: GET http://*************:6610/api/v1/datasets/efa077f07cc511f0815aea5dc8d5776c/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:30:57.507 | INFO     | 7c5714d68467455eb1d8094ec827fec3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/efa077f07cc511f0815aea5dc8d5776c/list/page=1&page_size=100 | 31.696ms
2025-08-19 14:31:34.227 | INFO     | 277049dd24cd461294fec43fa96ab326 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:31:34.229 | INFO     | 277049dd24cd461294fec43fa96ab326 | 成功认证Java用户: pythontest
2025-08-19 14:31:34.235 | INFO     | 277049dd24cd461294fec43fa96ab326 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:31:34.237 | INFO     | 277049dd24cd461294fec43fa96ab326 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:31:34.253 | INFO     | 277049dd24cd461294fec43fa96ab326 | HTTP Request: GET http://*************:6610/api/v1/datasets/efa077f07cc511f0815aea5dc8d5776c/documents?page=1&page_size=20&orderby=create_time&desc=false "HTTP/1.1 200 OK"
2025-08-19 14:31:34.254 | INFO     | 277049dd24cd461294fec43fa96ab326 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/efa077f07cc511f0815aea5dc8d5776c/list/page=1&page_size=20&orderby=create_time&desc=false | 27.231ms
2025-08-19 14:31:34.910 | INFO     | 0eb529d5d3df460c8d37259ae843580f | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:31:34.912 | INFO     | 0eb529d5d3df460c8d37259ae843580f | 成功认证Java用户: pythontest
2025-08-19 14:31:34.920 | INFO     | 0eb529d5d3df460c8d37259ae843580f | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:31:34.920 | INFO     | 0eb529d5d3df460c8d37259ae843580f | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:31:34.935 | INFO     | 0eb529d5d3df460c8d37259ae843580f | HTTP Request: GET http://*************:6610/api/v1/datasets/efa077f07cc511f0815aea5dc8d5776c/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:31:34.935 | INFO     | 0eb529d5d3df460c8d37259ae843580f | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/efa077f07cc511f0815aea5dc8d5776c/list/page=1&page_size=20&orderby=create_time&desc=true | 26.387ms
2025-08-19 14:31:36.001 | INFO     | 444d1d1a09704f919e72863e2f4ddd49 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:31:36.004 | INFO     | 444d1d1a09704f919e72863e2f4ddd49 | 成功认证Java用户: pythontest
2025-08-19 14:31:36.010 | INFO     | 444d1d1a09704f919e72863e2f4ddd49 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:31:36.011 | INFO     | 444d1d1a09704f919e72863e2f4ddd49 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:31:36.025 | INFO     | 444d1d1a09704f919e72863e2f4ddd49 | HTTP Request: GET http://*************:6610/api/v1/datasets/efa077f07cc511f0815aea5dc8d5776c/documents?page=1&page_size=20&orderby=create_time&desc=false "HTTP/1.1 200 OK"
2025-08-19 14:31:36.027 | INFO     | 444d1d1a09704f919e72863e2f4ddd49 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/efa077f07cc511f0815aea5dc8d5776c/list/page=1&page_size=20&orderby=create_time&desc=false | 26.142ms
2025-08-19 14:31:36.274 | INFO     | 36f08c4813c446f0bce0744b8f7409bb | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:31:36.276 | INFO     | 36f08c4813c446f0bce0744b8f7409bb | 成功认证Java用户: pythontest
2025-08-19 14:31:36.282 | INFO     | 36f08c4813c446f0bce0744b8f7409bb | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:31:36.283 | INFO     | 36f08c4813c446f0bce0744b8f7409bb | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:31:36.297 | INFO     | 36f08c4813c446f0bce0744b8f7409bb | HTTP Request: GET http://*************:6610/api/v1/datasets/efa077f07cc511f0815aea5dc8d5776c/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:31:36.298 | INFO     | 36f08c4813c446f0bce0744b8f7409bb | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/efa077f07cc511f0815aea5dc8d5776c/list/page=1&page_size=20&orderby=create_time&desc=true | 24.724ms
2025-08-19 14:31:41.721 | INFO     | 94c67b19e400418f81d7292c5d804709 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:31:41.722 | INFO     | 94c67b19e400418f81d7292c5d804709 | 成功认证Java用户: pythontest
2025-08-19 14:31:41.729 | INFO     | 94c67b19e400418f81d7292c5d804709 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:31:41.730 | INFO     | 94c67b19e400418f81d7292c5d804709 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:31:41.746 | INFO     | 94c67b19e400418f81d7292c5d804709 | HTTP Request: GET http://*************:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:31:41.747 | INFO     | 94c67b19e400418f81d7292c5d804709 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/87a3346677ee11f0a8f37e63526e5b16/list/page=1&page_size=20&orderby=create_time&desc=true | 25.442ms
2025-08-19 14:31:50.350 | INFO     | eae4eaa0b2ba491589890d5b982305b8 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:31:50.352 | INFO     | eae4eaa0b2ba491589890d5b982305b8 | 成功认证Java用户: pythontest
2025-08-19 14:31:50.355 | INFO     | eae4eaa0b2ba491589890d5b982305b8 | 127.0.0.1       | POST     | 422    | /api/iot/v1/documents/87a3346677ee11f0a8f37e63526e5b16/5b49ded077f611f083657e63526e5b16/parse | 4.907ms
2025-08-19 14:31:51.330 | INFO     | 4511a053d0964f7fa6401b17254ded7d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:31:51.333 | INFO     | 4511a053d0964f7fa6401b17254ded7d | 成功认证Java用户: pythontest
2025-08-19 14:31:51.336 | INFO     | 4511a053d0964f7fa6401b17254ded7d | 127.0.0.1       | POST     | 422    | /api/iot/v1/documents/87a3346677ee11f0a8f37e63526e5b16/5b49ded077f611f083657e63526e5b16/parse | 5.627ms
2025-08-19 14:31:51.804 | INFO     | 4edfea1762684eb59953d7e1b4af6a32 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:31:51.806 | INFO     | 4edfea1762684eb59953d7e1b4af6a32 | 成功认证Java用户: pythontest
2025-08-19 14:31:51.811 | INFO     | 4edfea1762684eb59953d7e1b4af6a32 | 127.0.0.1       | POST     | 422    | /api/iot/v1/documents/87a3346677ee11f0a8f37e63526e5b16/5b49ded077f611f083657e63526e5b16/parse | 8.161ms
2025-08-19 14:32:17.351 | INFO     | 66b06165b9e14e23b084918978c32df1 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:32:17.352 | INFO     | eaaa17a93c5246b49f115796bfa6a0a1 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:32:17.352 | INFO     | 630962c8bdb54af4ae3a66d4671fd037 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:32:17.352 | INFO     | 66b06165b9e14e23b084918978c32df1 | 成功认证Java用户: pythontest
2025-08-19 14:32:17.353 | INFO     | eaaa17a93c5246b49f115796bfa6a0a1 | 成功认证Java用户: pythontest
2025-08-19 14:32:17.354 | INFO     | 630962c8bdb54af4ae3a66d4671fd037 | 成功认证Java用户: pythontest
2025-08-19 14:32:17.362 | INFO     | eaaa17a93c5246b49f115796bfa6a0a1 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:32:17.363 | INFO     | eaaa17a93c5246b49f115796bfa6a0a1 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:32:17.366 | INFO     | 630962c8bdb54af4ae3a66d4671fd037 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:32:17.367 | INFO     | 630962c8bdb54af4ae3a66d4671fd037 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 14:32:17.371 | INFO     | 66b06165b9e14e23b084918978c32df1 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 14:32:17.373 | INFO     | 66b06165b9e14e23b084918978c32df1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 21.758ms
2025-08-19 14:32:17.388 | INFO     | eaaa17a93c5246b49f115796bfa6a0a1 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:32:17.388 | INFO     | 630962c8bdb54af4ae3a66d4671fd037 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:32:17.390 | INFO     | eaaa17a93c5246b49f115796bfa6a0a1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 37.934ms
2025-08-19 14:32:17.391 | INFO     | 630962c8bdb54af4ae3a66d4671fd037 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 38.770ms
2025-08-19 14:36:28.880 | INFO     | 3f1b5b32348b454cbbe82149e3ddf7cb | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:36:28.882 | INFO     | 3f1b5b32348b454cbbe82149e3ddf7cb | 成功认证Java用户: pythontest
2025-08-19 14:36:28.888 | INFO     | 3f1b5b32348b454cbbe82149e3ddf7cb | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:36:28.888 | INFO     | 3f1b5b32348b454cbbe82149e3ddf7cb | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:36:28.904 | INFO     | 3f1b5b32348b454cbbe82149e3ddf7cb | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:36:28.905 | INFO     | 3f1b5b32348b454cbbe82149e3ddf7cb | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 24.928ms
2025-08-19 14:36:28.913 | INFO     | 51e8995544354e2da5bfa2ef346f0c9a | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 14:36:28.915 | INFO     | 51e8995544354e2da5bfa2ef346f0c9a | 成功认证Java用户: pythontest
2025-08-19 14:36:28.921 | INFO     | 51e8995544354e2da5bfa2ef346f0c9a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 14:36:28.922 | INFO     | 51e8995544354e2da5bfa2ef346f0c9a | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 14:36:28.936 | INFO     | 51e8995544354e2da5bfa2ef346f0c9a | HTTP Request: GET http://*************:6610/api/v1/datasets/efa077f07cc511f0815aea5dc8d5776c/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 14:36:28.937 | INFO     | 51e8995544354e2da5bfa2ef346f0c9a | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/efa077f07cc511f0815aea5dc8d5776c/list/page=1&page_size=20&orderby=create_time&desc=true | 24.095ms
